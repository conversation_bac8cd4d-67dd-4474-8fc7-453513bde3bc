import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:design',
      order: 1, // 排在最前面
      title: '低代码设计器',
    },
    name: 'Designer',
    path: '/designer',
    redirect: '/designer/new',
    children: [
      {
        path: '/designer/new',
        name: 'NewDesigner',
        component: () => import('#/views/designer/NewDesigner.vue'),
        meta: {
          title: '新版设计器',
          icon: 'mdi:design',
          keepAlive: true,
          noBasicLayout: true
        },
      },
      {
        path: '/dmmcma/1djfjao',
        name: 'LegacyDesigner',
        component: () => import('#/views/designer/index.vue'),
        meta: {
          title: '原版设计器',
          icon: 'mdi:design-outline',
          keepAlive: true,
          noBasicLayout: true
        },
      },
      {
        path: '/standalone/designer',
        name: 'Designer1',
        component: () => import('#/views/designer/NewDesigner.vue'),
        meta: {
          icon: 'mdi:code-braces', // 图标，可选，根据需要添加
          title: '低代码',
          noBasicLayout: true, // 关键属性：不使用基础布局
          // hideInMenu: true, // 在菜单中隐藏
          // hideInTab: true, // 在标签页中隐藏
          ignoreAccess: true, // 忽略权限检查
        },
      },

    ],
  },
];

export default routes;
