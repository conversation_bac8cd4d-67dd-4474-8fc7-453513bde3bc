<script setup lang="ts">
import { ref, computed } from 'vue';
import { Modal, Select, Button, Space, message } from 'ant-design-vue';
import { MdiContentCopy, MdiDownload } from '@vben/icons';

const { SelectOption } = Select;

interface PageInfo {
  id: string;
  title: string;
  path: string;
  description?: string;
}

interface ComponentInfo {
  id: string;
  type: string;
  props: Record<string, any>;
  style: Record<string, any>;
  children?: ComponentInfo[];
}

interface Props {
  visible: boolean;
  pageList: PageInfo[];
  pagesComponentsMap: Map<string, ComponentInfo[]>;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 选中的页面ID和代码类型
const selectedPageId = ref(props.pageList[0]?.id || '');
const codeType = ref<'vue' | 'json' | 'html'>('json');

// 当前页面信息
const currentPage = computed(() => {
  return props.pageList.find(page => page.id === selectedPageId.value);
});

// 当前页面组件
const currentComponents = computed(() => {
  return props.pagesComponentsMap.get(selectedPageId.value) || [];
});

// 生成代码
const generatedCode = computed(() => {
  if (!currentPage.value) return '';

  switch (codeType.value) {
    case 'json':
      return generateJsonCode();
    case 'vue':
      return generateVueCode();
    case 'html':
      return generateHtmlCode();
    default:
      return '';
  }
});

// 生成JSON数据
function generateJsonCode(): string {
  const page = currentPage.value!;
  const components = currentComponents.value;

  const data = {
    page: {
      id: page.id,
      title: page.title,
      path: page.path,
      description: page.description,
    },
    components: components.map(component => ({
      id: component.id,
      type: component.type,
      props: component.props,
      style: component.style,
      children: component.children,
    })),
    meta: {
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      componentCount: components.length,
    },
  };

  return JSON.stringify(data, null, 2);
}

// 生成Vue组件代码
function generateVueCode(): string {
  const page = currentPage.value!;
  const components = currentComponents.value;

  return `<template>
  <div class="page-${page.id}">
    <div class="page-content">
${components.map(component => `      <${component.type} v-bind="components.${component.id}.props" />`).join('\n')}
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

const components = reactive({
${components.map(component => `  ${component.id}: ${JSON.stringify(component, null, 4)}`).join(',\n')}
});
</script>

<style scoped>
.page-${page.id} {
  min-height: 100vh;
  background: #f5f5f5;
}
</style>`;
}

// 生成HTML代码
function generateHtmlCode(): string {
  const page = currentPage.value!;
  const components = currentComponents.value;

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${page.title}</title>
</head>
<body>
  <div class="page-container">
    <h1>${page.title}</h1>
    <div class="components">
${components.map(component => `      <div class="component-${component.type}">${component.type}</div>`).join('\n')}
    </div>
  </div>
</body>
</html>`;
}

// 处理取消
function handleCancel() {
  emit('update:visible', false);
}

// 处理页面切换
function handlePageChange(pageId: string) {
  selectedPageId.value = pageId;
}

// 处理代码类型切换
function handleCodeTypeChange(type: 'vue' | 'json' | 'html') {
  codeType.value = type;
}

// 处理复制代码
async function handleCopy() {
  try {
    await navigator.clipboard.writeText(generatedCode.value);
    message.success('代码已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败');
  }
}

// 处理下载文件
function handleDownload() {
  const page = currentPage.value!;
  const filename = `${page.id}-${codeType.value === 'vue' ? 'component.vue' : codeType.value === 'json' ? 'data.json' : 'page.html'}`;

  const blob = new Blob([generatedCode.value], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
  message.success('文件下载成功');
}
</script>

<template>
  <Modal
    :open="visible"
    title="代码预览"
    width="80%"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="code-preview-container">
      <div class="preview-header">
        <div class="header-left">
          <Select
            v-model:value="selectedPageId"
            placeholder="选择页面"
            style="width: 200px"
            @change="handlePageChange"
          >
            <SelectOption
              v-for="page in pageList"
              :key="page.id"
              :value="page.id"
            >
              {{ page.title }}
            </SelectOption>
          </Select>

          <Select
            v-model:value="codeType"
            style="width: 150px; margin-left: 12px"
            @change="handleCodeTypeChange"
          >
            <SelectOption value="json">JSON 数据</SelectOption>
            <SelectOption value="vue">Vue 组件</SelectOption>
            <SelectOption value="html">HTML 代码</SelectOption>
          </Select>
        </div>

        <div class="header-right">
          <Space>
            <Button @click="handleCopy">
              <MdiContentCopy class="btn-icon" />
              复制代码
            </Button>
            <Button @click="handleDownload">
              <MdiDownload class="btn-icon" />
              下载文件
            </Button>
          </Space>
        </div>
      </div>

      <div class="code-content">
        <div class="code-editor">
          <pre><code>{{ generatedCode }}</code></pre>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.code-preview-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.header-left {
  display: flex;
  align-items: center;
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.code-content {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.code-editor {
  height: 100%;
  overflow: auto;
  background: #f8f8f8;
}

.code-editor pre {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-editor code {
  color: #333;
}
</style>
