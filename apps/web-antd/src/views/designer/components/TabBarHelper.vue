<template>
  <Modal
    v-model:open="visible"
    title="TabBar 快速配置"
    width="800px"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="tabbar-helper">
      <div class="helper-section">
        <h4>选择 TabBar 类型</h4>
        <Radio.Group v-model:value="selectedVariant" class="variant-selector">
          <Radio.Button value="bottom">底部标签栏</Radio.Button>
          <Radio.Button value="top">顶部标签栏</Radio.Button>
          <Radio.Button value="side">侧边标签栏</Radio.Button>
        </Radio.Group>
      </div>

      <div class="helper-section">
        <h4>配置标签项</h4>
        <div class="tabs-config">
          <div
            v-for="(tab, index) in tabsConfig"
            :key="index"
            class="tab-config-item"
          >
            <Card size="small" :title="`标签 ${index + 1}`">
              <template #extra>
                <Button
                  type="text"
                  danger
                  size="small"
                  @click="removeTab(index)"
                  :disabled="tabsConfig.length <= 1"
                >
                  删除
                </Button>
              </template>
              
              <div class="tab-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>标识</label>
                    <Input v-model:value="tab.name" placeholder="唯一标识" />
                  </div>
                  <div class="form-item">
                    <label>标题</label>
                    <Input v-model:value="tab.title" placeholder="显示标题" />
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-item">
                    <label>图标</label>
                    <Input v-model:value="tab.icon" placeholder="如：mdi:home" />
                  </div>
                  <div class="form-item">
                    <label>绑定页面</label>
                    <Select v-model:value="tab.pageId" placeholder="选择页面">
                      <Select.Option
                        v-for="page in availablePages"
                        :key="page.value"
                        :value="page.value"
                      >
                        {{ page.label }}
                      </Select.Option>
                    </Select>
                  </div>
                </div>
              </div>
            </Card>
          </div>
          
          <Button
            type="dashed"
            block
            @click="addTab"
            class="add-tab-btn"
          >
            <MdiPlus /> 添加标签
          </Button>
        </div>
      </div>

      <div class="helper-section">
        <h4>预览效果</h4>
        <div class="preview-container">
          <div class="preview-phone">
            <div class="preview-content">
              <div class="preview-tabbar" :class="`preview-${selectedVariant}`">
                <div
                  v-for="tab in tabsConfig"
                  :key="tab.name"
                  class="preview-tab"
                  :class="{ active: tab.name === previewActiveTab }"
                  @click="previewActiveTab = tab.name"
                >
                  <div class="preview-icon">
                    <IconifyIcon v-if="tab.icon" :icon="tab.icon" />
                    <span v-else>📱</span>
                  </div>
                  <div class="preview-title">{{ tab.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Modal, Radio, Card, Button, Input, Select } from 'ant-design-vue';
import { MdiPlus } from '@vben/icons';
import { Icon as IconifyIcon } from '@iconify/vue';
import { getRegisteredPages } from '@vben/lowcode/core';

interface TabConfig {
  name: string;
  title: string;
  icon: string;
  pageId: string;
}

const props = defineProps<{
  visible: boolean;
  initialConfig?: {
    variant: string;
    tabs: TabConfig[];
    editingComponent?: any;
  } | null;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'confirm': [config: { variant: string; tabs: TabConfig[] }];
}>();

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});

const selectedVariant = ref<'bottom' | 'top' | 'side'>('bottom');
const previewActiveTab = ref('home');

const tabsConfig = ref<TabConfig[]>([
  { name: 'home', title: '首页', icon: 'mdi:home', pageId: '' },
  { name: 'search', title: '搜索', icon: 'mdi:magnify', pageId: '' },
  { name: 'user', title: '我的', icon: 'mdi:account', pageId: '' },
]);

// 获取可用页面列表
const availablePages = computed(() => {
  return getRegisteredPages();
});

// 添加标签
function addTab() {
  const newIndex = tabsConfig.value.length + 1;
  tabsConfig.value.push({
    name: `tab${newIndex}`,
    title: `标签${newIndex}`,
    icon: 'mdi:circle',
    pageId: '',
  });
}

// 删除标签
function removeTab(index: number) {
  if (tabsConfig.value.length > 1) {
    tabsConfig.value.splice(index, 1);
  }
}

// 确认配置
function handleConfirm() {
  emit('confirm', {
    variant: selectedVariant.value,
    tabs: tabsConfig.value,
  });
  visible.value = false;
}

// 取消配置
function handleCancel() {
  visible.value = false;
}

// 监听第一个标签变化，更新预览
watch(
  () => tabsConfig.value[0]?.name,
  (newName) => {
    if (newName) {
      previewActiveTab.value = newName;
    }
  },
  { immediate: true }
);

// 监听初始配置变化，加载配置
watch(
  () => props.initialConfig,
  (config) => {
    if (config && props.visible) {
      selectedVariant.value = config.variant as 'bottom' | 'top' | 'side';
      if (config.tabs && config.tabs.length > 0) {
        tabsConfig.value = [...config.tabs];
        previewActiveTab.value = config.tabs[0]?.name || 'home';
      }
    }
  },
  { immediate: true }
);

// 监听visible变化，重置或加载配置
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      if (props.initialConfig) {
        // 加载现有配置
        selectedVariant.value = props.initialConfig.variant as 'bottom' | 'top' | 'side';
        if (props.initialConfig.tabs && props.initialConfig.tabs.length > 0) {
          tabsConfig.value = [...props.initialConfig.tabs];
          previewActiveTab.value = props.initialConfig.tabs[0]?.name || 'home';
        }
      } else {
        // 重置为默认配置
        selectedVariant.value = 'bottom';
        tabsConfig.value = [
          { name: 'home', title: '首页', icon: 'mdi:home', pageId: '' },
          { name: 'search', title: '搜索', icon: 'mdi:magnify', pageId: '' },
          { name: 'user', title: '我的', icon: 'mdi:account', pageId: '' },
        ];
        previewActiveTab.value = 'home';
      }
    }
  }
);
</script>

<style scoped>
.tabbar-helper {
  max-height: 600px;
  overflow-y: auto;
}

.helper-section {
  margin-bottom: 24px;
}

.helper-section h4 {
  margin-bottom: 12px;
  font-weight: 600;
}

.variant-selector {
  width: 100%;
}

.tabs-config {
  max-height: 300px;
  overflow-y: auto;
}

.tab-config-item {
  margin-bottom: 16px;
}

.tab-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-row {
  display: flex;
  gap: 12px;
}

.form-item {
  flex: 1;
}

.form-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

.add-tab-btn {
  margin-top: 12px;
}

.preview-container {
  display: flex;
  justify-content: center;
}

.preview-phone {
  width: 200px;
  height: 300px;
  border: 2px solid #ddd;
  border-radius: 12px;
  background: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.preview-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.preview-tabbar {
  position: absolute;
  display: flex;
  background: white;
  border: 1px solid #eee;
}

.preview-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  flex-direction: row;
}

.preview-top {
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  flex-direction: row;
}

.preview-side {
  left: 0;
  top: 0;
  bottom: 0;
  width: 60px;
  flex-direction: column;
}

.preview-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  font-size: 10px;
  transition: color 0.2s;
}

.preview-tab:hover,
.preview-tab.active {
  color: #1890ff;
}

.preview-icon {
  margin-bottom: 2px;
  font-size: 16px;
}

.preview-title {
  font-size: 8px;
}

.preview-side .preview-tab {
  padding: 8px 4px;
}

.preview-side .preview-icon {
  margin-bottom: 4px;
}
</style>
