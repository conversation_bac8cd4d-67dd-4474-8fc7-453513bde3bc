<template>
  <Modal
    :open="visible"
    title="页面管理"
    width="900px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="page-manager">
      <div class="manager-header">
        <div class="header-info">
          <h3>页面列表</h3>
          <span class="page-count">共 {{ pageList.length }} 个页面</span>
        </div>
        <div class="header-actions">
          <Button type="primary" @click="handleAddPage">
            <MdiPlus class="btn-icon" />
            新建页面
          </Button>
        </div>
      </div>

      <div class="page-grid">
        <div
          v-for="page in pageList"
          :key="page.id"
          class="page-card"
          :class="{ active: page.id === currentPageId }"
        >
          <div class="page-preview">
            <div class="preview-header">
              <div class="preview-status-bar">
                <span class="time">9:41</span>
                <div class="indicators">
                  <MdiSignal class="indicator" />
                  <MdiWifi class="indicator" />
                  <MdiBattery class="indicator" />
                </div>
              </div>
            </div>
            <div class="preview-content">
              <div class="preview-placeholder">
                <MdiFileDocument class="placeholder-icon" />
                <span class="placeholder-text">{{ page.title }}</span>
              </div>
            </div>
          </div>
          
          <div class="page-info">
            <div class="page-details">
              <h4 class="page-title">{{ page.title }}</h4>
              <p class="page-path">{{ page.path }}</p>
              <div class="page-meta">
                <span class="meta-item">
                  <MdiCube class="meta-icon" />
                  {{ getPageComponentCount(page.id) }} 个组件
                </span>
                <span class="meta-item">
                  <MdiClock class="meta-icon" />
                  最近编辑
                </span>
              </div>
            </div>
            
            <div class="page-actions">
              <Tooltip title="编辑页面">
                <Button size="small" type="text" @click="handleSwitchPage(page.id)">
                  <MdiPencil class="action-icon" />
                </Button>
              </Tooltip>
              <Tooltip title="页面设置">
                <Button size="small" type="text" @click="handleEditPage(page)">
                  <MdiCog class="action-icon" />
                </Button>
              </Tooltip>
              <Tooltip title="复制页面">
                <Button size="small" type="text" @click="handleDuplicatePage(page)">
                  <MdiContentDuplicate class="action-icon" />
                </Button>
              </Tooltip>
              <Tooltip title="删除页面">
                <Button 
                  size="small" 
                  type="text" 
                  danger 
                  @click="handleDeletePage(page.id)"
                  :disabled="pageList.length <= 1"
                >
                  <MdiDelete class="action-icon" />
                </Button>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建/编辑页面弹窗 -->
    <Modal
      v-model:open="showPageForm"
      :title="editingPage ? '编辑页面' : '新建页面'"
      width="500px"
      @ok="handlePageFormOk"
      @cancel="handlePageFormCancel"
    >
      <Form :model="pageForm" layout="vertical" ref="pageFormRef">
        <FormItem 
          label="页面标题" 
          name="title" 
          :rules="[{ required: true, message: '请输入页面标题' }]"
        >
          <Input 
            v-model:value="pageForm.title" 
            placeholder="请输入页面标题"
            @input="handleTitleChange"
          />
        </FormItem>
        
        <FormItem 
          label="页面ID" 
          name="id" 
          :rules="[
            { required: true, message: '请输入页面ID' },
            { pattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/, message: '页面ID必须以字母开头，只能包含字母、数字、下划线和连字符' }
          ]"
        >
          <Input 
            v-model:value="pageForm.id" 
            placeholder="请输入页面ID，如：home"
            :disabled="!!editingPage"
          />
        </FormItem>
        
        <FormItem 
          label="页面路径" 
          name="path" 
          :rules="[{ required: true, message: '请输入页面路径' }]"
        >
          <Input 
            v-model:value="pageForm.path" 
            placeholder="请输入页面路径，如：/pages/home"
          />
        </FormItem>
        
        <FormItem label="页面描述" name="description">
          <Input.TextArea 
            v-model:value="pageForm.description" 
            placeholder="请输入页面描述（可选）"
            :rows="3"
          />
        </FormItem>
      </Form>
    </Modal>
  </Modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Modal, Button, Tooltip, Form, Input, message } from 'ant-design-vue';
import {
  MdiPlus,
  MdiSignal,
  MdiWifi,
  MdiBattery,
  MdiFileDocument,
  MdiCube,
  MdiClock,
  MdiPencil,
  MdiCog,
  MdiContentDuplicate,
  MdiDelete,
} from '@vben/icons';

const { FormItem } = Form;

interface PageInfo {
  id: string;
  title: string;
  path: string;
  description?: string;
}

interface Props {
  visible: boolean;
  pageList: PageInfo[];
  currentPageId: string;
  pagesComponentsMap?: Map<string, any[]>;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'page-switch', pageId: string): void;
  (e: 'page-add', page: PageInfo): void;
  (e: 'page-edit', page: PageInfo): void;
  (e: 'page-duplicate', page: PageInfo): void;
  (e: 'page-delete', pageId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 页面表单相关
const showPageForm = ref(false);
const editingPage = ref<PageInfo | null>(null);
const pageFormRef = ref();
const pageForm = reactive({
  id: '',
  title: '',
  path: '',
  description: '',
});

// 处理取消
function handleCancel() {
  emit('update:visible', false);
}

// 处理添加页面
function handleAddPage() {
  editingPage.value = null;
  pageForm.id = '';
  pageForm.title = '';
  pageForm.path = '';
  pageForm.description = '';
  showPageForm.value = true;
}

// 处理编辑页面
function handleEditPage(page: PageInfo) {
  editingPage.value = page;
  pageForm.id = page.id;
  pageForm.title = page.title;
  pageForm.path = page.path;
  pageForm.description = page.description || '';
  showPageForm.value = true;
}

// 处理页面切换
function handleSwitchPage(pageId: string) {
  emit('page-switch', pageId);
  emit('update:visible', false);
}

// 处理复制页面
function handleDuplicatePage(page: PageInfo) {
  const newPage = {
    ...page,
    id: `${page.id}_copy_${Date.now()}`,
    title: `${page.title} - 副本`,
    path: `${page.path}_copy`,
  };
  emit('page-duplicate', newPage);
}

// 处理删除页面
function handleDeletePage(pageId: string) {
  if (props.pageList.length <= 1) {
    message.warning('至少需要保留一个页面');
    return;
  }
  
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个页面吗？删除后无法恢复。',
    onOk() {
      emit('page-delete', pageId);
    },
  });
}

// 处理标题变化，自动生成ID和路径
function handleTitleChange() {
  if (!editingPage.value && pageForm.title) {
    // 自动生成ID（转换为小写，替换空格为下划线）
    const autoId = pageForm.title
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')
      .replace(/_{2,}/g, '_')
      .replace(/^_|_$/g, '');
    
    if (autoId && /^[a-zA-Z]/.test(autoId)) {
      pageForm.id = autoId;
      pageForm.path = `/pages/${autoId}`;
    }
  }
}

// 处理页面表单确认
async function handlePageFormOk() {
  try {
    await pageFormRef.value.validate();
    
    const pageData = {
      id: pageForm.id,
      title: pageForm.title,
      path: pageForm.path,
      description: pageForm.description,
    };
    
    if (editingPage.value) {
      emit('page-edit', pageData);
    } else {
      emit('page-add', pageData);
    }
    
    showPageForm.value = false;
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 处理页面表单取消
function handlePageFormCancel() {
  showPageForm.value = false;
}

// 获取页面组件数量
function getPageComponentCount(pageId: string): number {
  if (!props.pagesComponentsMap) return 0;
  const components = props.pagesComponentsMap.get(pageId);
  return components ? components.length : 0;
}
</script>

<style scoped>
.page-manager {
  padding: 16px 0;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.page-count {
  font-size: 14px;
  color: #666;
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.page-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.page-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
  background: #fff;
}

.page-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24,144,255,0.15);
}

.page-card.active {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

.page-preview {
  height: 160px;
  background: #f5f5f5;
  position: relative;
}

.preview-header {
  height: 24px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  color: #fff;
  font-size: 11px;
}

.indicators {
  display: flex;
  gap: 2px;
}

.indicator {
  width: 12px;
  height: 12px;
}

.preview-content {
  height: calc(100% - 24px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-placeholder {
  text-align: center;
  color: #999;
}

.placeholder-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 12px;
}

.page-info {
  padding: 16px;
}

.page-details {
  margin-bottom: 12px;
}

.page-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.page-path {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #999;
}

.page-meta {
  display: flex;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.meta-icon {
  width: 12px;
  height: 12px;
}

.page-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
}

.action-icon {
  width: 14px;
  height: 14px;
}
</style>
