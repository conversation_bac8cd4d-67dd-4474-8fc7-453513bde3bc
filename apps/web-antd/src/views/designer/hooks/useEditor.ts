import { computed, reactive, ref } from 'vue';

import { AllComponents } from '@vben/lowcode/components';
import { componentRegistry, ProjectManager, createProjectManager } from '@vben/lowcode/core';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { nanoid } from 'nanoid';

export interface PageInfo {
  id: string;
  path: string;
  title: string;
}

export interface EditorState {
  canvasLoading: boolean;
  isPhonePreview: boolean;
  siderCollapsed: boolean;
  codeModalVisible: boolean;
  previewVisible: boolean;
  currentTab: string;
  selectedCategory: string;
  pageManagerVisible: boolean;
}

export function useEditor() {
  // 项目管理器
  const projectManager = ref<ProjectManager>(createProjectManager());

  // 页面状态
  const editorState = reactive<EditorState>({
    canvasLoading: false,
    isPhonePreview: true,
    siderCollapsed: false,
    codeModalVisible: false,
    previewVisible: false,
    currentTab: 'components',
    selectedCategory: 'basic',
    pageManagerVisible: false,
  });

  // 页面管理
  const pageList = ref<PageInfo[]>([
    { id: 'home', title: '首页', path: '/pages/home' },
    { id: 'category', title: '分类', path: '/pages/category' },
    { id: 'cart', title: '购物车', path: '/pages/cart' },
    { id: 'user', title: '我的', path: '/pages/user' },
  ]);

  // 当前编辑的页面ID
  const currentPageId = ref('home');



  // 选中的组件
  const selectedComponent = ref<any>(null);

  // 选中的菜单项
  const selectedKeys = computed(() => [editorState.selectedCategory]);

  // 存储每个页面的组件列表
  const pagesComponentsMap = reactive(new Map<string, any[]>());

  // 初始化页面组件映射
  function initPagesMap() {
    pageList.value.forEach((page) => {
      if (!pagesComponentsMap.has(page.id)) {
        pagesComponentsMap.set(page.id, []);
      }
    });
  }

  // 注册页面到组件注册中心
  function registerPages() {
    componentRegistry.registerPages(pageList.value);
  }

  // 初始化
  initPagesMap();
  registerPages();

  // 画布中的组件列表
  const canvasComponents = computed({
    get: () => {
      return pagesComponentsMap.get(currentPageId.value) || [];
    },
    set: (value) => {
      pagesComponentsMap.set(currentPageId.value, value);
    },
  });

  // 切换当前编辑的页面
  function switchPage(pageId: string) {
    // 如果页面ID不存在，返回
    if (!pageList.value.find((p) => p.id === pageId)) {
      message.error('页面不存在');
      return false;
    }

    // 保存当前页面组件状态
    selectedComponent.value = null;
    currentPageId.value = pageId;

    return true;
  }

  // 从物料面板克隆组件到画布
  function handleClone(original) {
    // 创建一个新组件
    const newComponent = {
      id: nanoid(),
      type: original.type,
      label: original.label || original.type,
      props: original.defaultProps ? cloneDeep(original.defaultProps) : {},
      style: {},
      // 如果是容器类型组件，添加 children 数组
      children: [
        'column',
        'container',
        'grid',
        'row',
      ].includes(original.type)
        ? []
        : undefined,
      // 保留属性描述
      propsSchema: original.propsSchema || [],
    };

    return newComponent;
  }

  // 在画布中选中组件
  function selectComponent(component) {
    if (component) {
      // 从组件注册表中获取组件定义
      const componentDef = componentRegistry.getComponentDefinition(component.type);

      // 合并组件实例和组件定义的属性配置
      selectedComponent.value = {
        ...component,
        propsSchema: componentDef?.propsSchema || [],
        label: componentDef?.label || component.type,
      };
    } else {
      selectedComponent.value = null;
    }
  }

  // 删除组件
  function deleteComponent(id, updateCurrent = true) {
    // 递归查找并删除组件
    function deleteFromComponents(components) {
      const index = components.findIndex((c) => c.id === id);
      if (index !== -1) {
        components.splice(index, 1);
        return true;
      }

      // 如果没找到，检查子组件
      for (const comp of components) {
        if (comp.children && Array.isArray(comp.children)) {
          const found = deleteFromComponents(comp.children);
          if (found) return true;
        }
      }

      return false;
    }

    deleteFromComponents(canvasComponents.value);

    // 如果当前选中的是被删除的组件，取消选中
    if (updateCurrent && selectedComponent.value?.id === id) {
      selectedComponent.value = null;
    }
  }

  // 复制组件
  function duplicateComponent(component) {
    if (!component) return;

    const newComponent = cloneDeep(component);
    newComponent.id = nanoid();

    // 如果存在children，递归更新所有子组件的ID
    function updateChildrenIds(children) {
      if (!children || !Array.isArray(children)) return;

      for (const child of children) {
        child.id = nanoid();
        if (child.children && Array.isArray(child.children)) {
          updateChildrenIds(child.children);
        }
      }
    }

    if (newComponent.children && Array.isArray(newComponent.children)) {
      updateChildrenIds(newComponent.children);
    }

    // 查找组件在画布中的位置并插入
    function findAndInsert(components, targetId) {
      const index = components.findIndex((c) => c.id === targetId);
      if (index !== -1) {
        components.splice(index + 1, 0, newComponent);
        return true;
      }

      // 如果没找到，检查子组件
      for (const comp of components) {
        if (comp.children && Array.isArray(comp.children)) {
          const found = findAndInsert(comp.children, targetId);
          if (found) return true;
        }
      }

      return false;
    }

    findAndInsert(canvasComponents.value, component.id);
  }

  // 生成组件代码
  function generateComponentCode() {
    return JSON.stringify(canvasComponents.value, null, 2);
  }

  // 保存页面
  function savePage() {
    editorState.canvasLoading = true;

    // 更新项目管理器中的数据
    const designerData = {
      pageList: pageList.value,
      pagesComponentsMap,
    };

    const projectConfig = ProjectManager.fromDesignerData(designerData);
    projectManager.value.updateProject(projectConfig);

    // 这里可以添加实际保存逻辑，如API调用等
    setTimeout(() => {
      editorState.canvasLoading = false;
      message.success('页面保存成功');
    }, 800);
  }

  // 导出项目配置（供H5端使用）
  function exportProjectConfig() {
    const designerData = {
      pageList: pageList.value,
      pagesComponentsMap,
    };

    const projectConfig = ProjectManager.fromDesignerData(designerData);
    return projectConfig;
  }

  // 导出项目JSON
  function exportProjectJSON() {
    const projectConfig = exportProjectConfig();
    return JSON.stringify(projectConfig, null, 2);
  }

  // 物料组件列表按分类
  const componentCategories = computed(() => {
    const categories: Record<string, any[]> = {};

    AllComponents.forEach((comp) => {
      const category = comp.category || 'basic';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push({
        ...comp,
        id: comp.type,
      });
    });

    return categories;
  });

  // 所有可用分类
  const availableCategories = computed(() => {
    return Object.keys(componentCategories.value).sort();
  });

  // 当前选中分类的组件
  const currentCategoryComponents = computed(() => {
    return componentCategories.value[editorState.selectedCategory] || [];
  });

  // 检查是否是容器组件
  function isContainer(component) {
    if (!component) return false;

    // 查找组件定义中的 isContainer 属性
    const componentDef = AllComponents.find((c) => c.type === component.type);
    return (
      componentDef?.isContainer === true ||
      ['column', 'container', 'grid', 'row'].includes(
        component.type,
      )
    );
  }

  // 页面管理功能
  function openPageManager() {
    editorState.pageManagerVisible = true;
  }

  // 添加页面
  function addPage(page: PageInfo) {
    if (!page.id || !page.title || !page.path) {
      message.error('页面信息不完整');
      return false;
    }

    // 检查ID是否已存在
    if (pageList.value.some((p) => p.id === page.id)) {
      message.error('页面ID已存在');
      return false;
    }

    pageList.value.push({
      id: page.id,
      title: page.title,
      path: page.path,
    });

    // 初始化新页面的组件映射
    pagesComponentsMap.set(page.id, []);

    // 重新注册页面
    registerPages();

    message.success('页面添加成功');
    return true;
  }

  // 删除页面
  function deletePage(id: string) {
    // 不能删除当前正在编辑的页面
    if (id === currentPageId.value) {
      message.error('不能删除当前正在编辑的页面');
      return false;
    }

    pageList.value = pageList.value.filter((page) => page.id !== id);

    // 删除页面组件映射
    pagesComponentsMap.delete(id);



    // 重新注册页面
    registerPages();

    message.success('页面删除成功');
    return true;
  }



  return {
    editorState,
    pageList,
    currentPageId,
    canvasComponents,
    selectedComponent,
    selectedKeys,
    componentCategories,
    availableCategories,
    currentCategoryComponents,
    pagesComponentsMap,
    projectManager,
    // 页面管理
    switchPage,
    handleClone,
    selectComponent,
    deleteComponent,
    duplicateComponent,
    generateComponentCode,
    savePage,
    exportProjectConfig,
    exportProjectJSON,
    isContainer,
    openPageManager,
    addPage,
    deletePage,
  };
}
