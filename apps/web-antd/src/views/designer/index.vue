<script setup lang="ts">
import { onMounted, reactive } from 'vue';

import {
  MdiArrowCollapseRight,
  MdiArrowExpandRight,
  MdiCodeBraces,
  MdiContentDuplicate,
  MdiContentSave,
  MdiDelete,
  MdiEye,
  MdiHistoryUndo,
  MdiUndo,
} from '@vben/icons';
import { componentRegistry, ComponentRenderer } from '@vben/lowcode/core';

import {
  Button,
  Card,
  Collapse,
  CollapsePanel,
  Divider,
  Drawer,
  Dropdown,
  Empty,
  Form,
  FormItem,
  Input,
  Layout,
  LayoutContent,
  LayoutHeader,
  LayoutSider,
  Menu,
  MenuItem,
  message,
  Modal,
  Spin,
  Switch,
  TabPane,
  Tabs,
  Tag,
  Tooltip,
} from 'ant-design-vue';
import Draggable from 'vuedraggable';

// 导入自定义钩子
import { useEditor } from './hooks/useEditor';
import { useHistory } from './hooks/useHistory';
import { useProperties } from './hooks/useProperties';

// 导入组件
import TabBarConfig from './components/TabBarConfig.vue';

// 使用自定义钩子
const {
  editorState,
  pageList,
  currentPageId,
  canvasComponents,
  selectedComponent,
  selectedKeys,
  componentCategories,
  availableCategories,
  currentCategoryComponents,
  projectManager,
  tabBarItems,
  activeTabId,
  switchPage,
  handleClone,
  selectComponent,
  deleteComponent,
  duplicateComponent,
  generateComponentCode,
  savePage,
  exportProjectConfig,
  exportProjectJSON,
  isContainer,
  openPageManager,
  addPage,
  deletePage,
  toggleTabBar,
  openTabBarConfig,
  addTabBarItem,
  removeTabBarItem,
  updateTabBarItem,
  switchTab,
} = useEditor();

// 使用历史记录钩子
const {
  history,
  initPageHistory,
  saveHistory,
  undo,
  redo,
  clearPageHistory,
  switchPageHistory,
} = useHistory(currentPageId);

// 初始化页面历史记录
pageList.value.forEach((page) => {
  initPageHistory(page.id);
});

// 使用属性编辑器钩子
const { propsForm, renderPropEditor } = useProperties(
  selectedComponent,
  componentRegistry,
);

// 新页面表单
const newPage = reactive({
  id: '',
  title: '',
  path: '',
});

// 预览和代码相关功能
function previewPage() {
  editorState.previewVisible = true;
}

function openCodeModal() {
  editorState.codeModalVisible = true;
}

// 导出项目配置
function exportProject() {
  const projectJSON = exportProjectJSON();

  // 创建下载链接
  const blob = new Blob([projectJSON], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `lowcode-project-${Date.now()}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  message.success('项目配置已导出');
}

// 拖拽事件处理
function onDragStart() {
  // 拖拽开始时的处理
}

function onDragEnd() {
  // 拖拽结束后保存历史记录
  saveHistory(canvasComponents.value);
}

// 执行撤销操作
function handleUndo() {
  if (undo(canvasComponents)) {
    message.success('撤销成功');
  }
}

// 执行重做操作
function handleRedo() {
  if (redo(canvasComponents)) {
    message.success('重做成功');
  }
}

// 切换页面
function handleSwitchPage(pageId) {
  if (switchPage(pageId)) {
    switchPageHistory(pageId);
    message.success(
      `已切换到页面: ${pageList.value.find((p) => p.id === pageId)?.title}`,
    );
  }
}

// 添加新页面
function handleAddPage() {
  if (
    addPage({
      id: newPage.id,
      title: newPage.title,
      path: newPage.path,
    })
  ) {
    // 初始化新页面的历史记录
    initPageHistory(newPage.id);

    // 重置表单
    newPage.id = '';
    newPage.title = '';
    newPage.path = '';
  }
}

// 更新选中分类
function updateSelectedCategory(category) {
  editorState.selectedCategory = category;
}

onMounted(() => {
  Modal.info({
    title: '温馨提示',
    content: '低代码设计器正在开发中，部分功能可能不完善。',
  });
});
</script>

<template>
  <Layout class="lowcode-editor">
    <!-- 左侧组件面板 -->
    <LayoutSider
      class="materials-sider"
      :collapsed="editorState.siderCollapsed"
      :collapsible="true"
      collapsed-width="50"
      :width="280"
      :trigger="null"
      theme="light"
    >
      <!-- 收起状态 -->
      <div
        v-if="editorState.siderCollapsed"
        class="flex h-full flex-col py-4 text-center"
      >
        <div
          class="mb-2 cursor-pointer rounded p-2 hover:bg-blue-50"
          @click="editorState.siderCollapsed = false"
        >
          <MdiArrowExpandRight style="font-size: 16px" />
        </div>
        <Divider class="my-2" />
        <div
          v-for="category in availableCategories"
          :key="category"
          class="mb-2 cursor-pointer rounded p-2 hover:bg-blue-50"
          :class="{ 'bg-blue-50': editorState.selectedCategory === category }"
          @click="updateSelectedCategory(category)"
        >
          {{ category.substring(0, 1).toUpperCase() }}
        </div>
      </div>

      <!-- 展开状态 -->
      <div v-else class="materials-panel">
        <div class="materials-header">
          <div class="flex items-center justify-between">
            <span class="text-lg font-semibold">组件库</span>
            <MdiArrowCollapseRight
              class="cursor-pointer hover:text-blue-500"
              style="font-size: 16px"
              @click="editorState.siderCollapsed = true"
            />
          </div>

          <Tabs
            :active-key="editorState.currentTab"
            class="mt-2"
            @update:active-key="(key) => (editorState.currentTab = key)"
          >
            <TabPane key="components" tab="组件">
              <Menu mode="inline" :selected-keys="selectedKeys">
                <MenuItem
                  v-for="category in availableCategories"
                  :key="category"
                  @click="updateSelectedCategory(category)"
                >
                  {{ category === 'basic' ? '基础组件' : category }}
                </MenuItem>
              </Menu>

              <Divider class="my-2">
                {{
                  editorState.selectedCategory === 'basic'
                    ? '基础组件'
                    : editorState.selectedCategory
                }}
              </Divider>

              <div class="component-list">
                <Draggable
                  :list="currentCategoryComponents"
                  :group="{ name: 'components', pull: 'clone', put: false }"
                  :sort="false"
                  :clone="handleClone"
                  item-key="id"
                  class="grid grid-cols-2 gap-2 p-2"
                >
                  <template #item="{ element }">
                    <Card
                      hoverable
                      :body-style="{ padding: '8px', textAlign: 'center' }"
                      class="component-card"
                    >
                      <div class="component-preview">
                        <div v-if="element.icon" class="component-icon">
                          <component
                            :is="element.icon"
                            style="font-size: 24px"
                          />
                        </div>
                        <div v-else class="component-placeholder">
                          {{ element.type.substring(0, 1).toUpperCase() }}
                        </div>
                      </div>
                      <div class="mt-2 text-sm font-medium">
                        {{ element.label }}
                      </div>
                    </Card>
                  </template>
                </Draggable>
              </div>
            </TabPane>
            <TabPane key="layers" tab="图层">
              <Empty
                description="暂无组件"
                v-if="canvasComponents.length === 0"
              />
              <ul v-else class="layer-list">
                <li
                  v-for="comp in canvasComponents"
                  :key="comp.id"
                  :class="{ active: selectedComponent?.id === comp.id }"
                  @click="selectComponent(comp)"
                >
                  <div class="flex items-center justify-between">
                    <div class="text-sm">{{ comp.label || comp.type }}</div>
                    <div class="layer-actions">
                      <span
                        class="action-icon delete-icon"
                        @click.stop="deleteComponent(comp.id)"
                      >
                        ×
                      </span>
                    </div>
                  </div>
                </li>
              </ul>
            </TabPane>
          </Tabs>
        </div>
      </div>
    </LayoutSider>

    <!-- 中间内容区 -->
    <Layout>
      <!-- 顶部工具栏 -->
      <LayoutHeader class="header-toolbar">
        <div class="toolbar-container">
          <div class="toolbar-left">
            <h2 class="text-lg font-semibold">低代码页面设计器</h2>
          </div>
          <div class="toolbar-right">
            <div class="toolbar-item mr-4">
              <span class="mr-2 text-sm">当前页面:</span>
              <Dropdown>
                <Button>
                  {{ pageList.find((p) => p.id === currentPageId)?.title }}
                  <i class="ml-1">▼</i>
                </Button>
                <template #overlay>
                  <Menu>
                    <MenuItem
                      v-for="page in pageList"
                      :key="page.id"
                      @click="handleSwitchPage(page.id)"
                    >
                      {{ page.title }}
                    </MenuItem>
                  </Menu>
                </template>
              </Dropdown>
            </div>

            <Button class="toolbar-btn mr-2" type="primary" @click="savePage">
              <MdiContentSave class="mr-1" />
              保存
            </Button>
            <Button
              class="toolbar-btn mr-2"
              :disabled="!history.past[currentPageId]?.length"
              @click="handleUndo"
            >
              <MdiHistoryUndo class="mr-1" />
              撤销
            </Button>
            <Button
              class="toolbar-btn mr-2"
              :disabled="!history.future[currentPageId]?.length"
              @click="handleRedo"
            >
              <MdiUndo class="mr-1" />
              重做
            </Button>
            <Button class="toolbar-btn mr-2" @click="previewPage">
              <MdiEye class="mr-1" />
              预览
            </Button>
            <Button class="toolbar-btn mr-2" @click="openCodeModal">
              <MdiCodeBraces class="mr-1" />
              代码
            </Button>
            <Button class="toolbar-btn mr-2" @click="exportProject">
              <MdiContentSave class="mr-1" />
              导出
            </Button>
            <Button class="toolbar-btn mr-2" @click="openPageManager">
              管理页面
            </Button>
            <Button class="toolbar-btn mr-4" @click="openTabBarConfig">
              TabBar配置
            </Button>
          </div>
        </div>
      </LayoutHeader>

      <!-- 画布区域 -->
      <LayoutContent class="content-area">
        <div
          class="canvas-container"
          :class="{ 'phone-preview': editorState.isPhonePreview }"
        >
          <div class="canvas-header">
            <div class="flex items-center">
              <Switch
                :checked="editorState.isPhonePreview"
                class="mr-2"
                @update:checked="
                  (checked) => (editorState.isPhonePreview = checked)
                "
              />
              <span>手机视图</span>
            </div>
          </div>

          <div class="canvas-wrapper">
            <Spin :spinning="editorState.canvasLoading" tip="加载中...">
              <div
                class="canvas"
                :class="{ 'phone-screen': editorState.isPhonePreview }"
              >
                <!-- 画布组件区域 -->
                <Draggable
                  v-model="canvasComponents"
                  :group="{ name: 'components', pull: false, put: true }"
                  item-key="id"
                  class="canvas-draggable"
                  @start="onDragStart"
                  @end="onDragEnd"
                >
                  <template #item="{ element }">
                    <div
                      class="component-wrapper"
                      :class="{
                        selected: selectedComponent?.id === element.id,
                      }"
                      @click="selectComponent(element)"
                    >
                      <div class="component-controls">
                        <div class="drag-handle">
                          <span class="component-name">{{
                            element.label || element.type
                          }}</span>
                          <div class="control-buttons">
                            <Tooltip title="复制">
                              <MdiContentDuplicate
                                class="control-btn"
                                @click.stop="duplicateComponent(element)"
                              />
                            </Tooltip>
                            <Tooltip title="删除">
                              <MdiDelete
                                class="control-btn"
                                @click.stop="deleteComponent(element.id)"
                              />
                            </Tooltip>
                          </div>
                        </div>
                      </div>

                      <!-- TabBarContainer特殊处理 -->
                      <template v-if="element.type === 'TabBarContainer'">
                        <ComponentRenderer
                          :type="element.type"
                          :component-props="{
                            ...element.props,
                            designMode: true,
                            pageList,
                            activeTabId:
                              element.props.activeTabId ||
                              (element.props.modelValue &&
                              element.props.modelValue.length > 0
                                ? element.props.modelValue[0].id
                                : ''),
                          }"
                          :style="element.style"
                          :id="element.id"
                          :children="element.children"
                          @update:model-value="
                            (value) => {
                              element.props.modelValue = value;
                              saveHistory(canvasComponents.value);
                            }
                          "
                          @update:active-tab-id="
                            (value) => {
                              element.props.activeTabId = value;
                            }
                          "
                        />
                      </template>

                      <!-- 普通容器组件 -->
                      <template v-if="isContainer(element)">
                        <div class="container-component">
                          <ComponentRenderer
                            :type="element.type"
                            :component-props="element.props"
                            :style="element.style"
                            :id="element.id"
                          />
                          <!-- 嵌套拖拽区域 -->
                          <Draggable
                            v-model="element.children"
                            :group="{
                              name: 'nested-components',
                              pull: true,
                              put: true,
                            }"
                            item-key="id"
                            class="nested-draggable"
                            @start="onDragStart"
                            @end="onDragEnd"
                          >
                            <template #item="{ element: child }">
                              <div
                                class="component-wrapper nested-component"
                                :class="{
                                  selected: selectedComponent?.id === child.id,
                                }"
                                @click.stop="selectComponent(child)"
                              >
                                <div class="component-controls">
                                  <div class="drag-handle">
                                    <span class="component-name">{{
                                      child.label || child.type
                                    }}</span>
                                    <div class="control-buttons">
                                      <Tooltip title="复制">
                                        <MdiContentDuplicate
                                          class="control-btn"
                                          @click.stop="
                                            duplicateComponent(child)
                                          "
                                        />
                                      </Tooltip>
                                      <Tooltip title="删除">
                                        <MdiDelete
                                          class="control-btn"
                                          @click.stop="
                                            deleteComponent(child.id)
                                          "
                                        />
                                      </Tooltip>
                                    </div>
                                  </div>
                                </div>
                                <ComponentRenderer
                                  :type="child.type"
                                  :component-props="child.props"
                                  :style="child.style"
                                  :id="child.id"
                                  :children="child.children"
                                />
                              </div>
                            </template>
                          </Draggable>
                        </div>
                      </template>

                      <!-- 普通组件 -->
                      <template v-else>
                        <ComponentRenderer
                          :type="element.type"
                          :component-props="element.props"
                          :style="element.style"
                          :id="element.id"
                        />
                      </template>
                    </div>
                  </template>
                </Draggable>

                <!-- 空画布提示 -->
                <div v-if="canvasComponents.length === 0" class="empty-canvas">
                  <Empty description="拖拽组件到此处创建页面">
                    <template #description>
                      <div class="text-gray-500">
                        从左侧组件库拖拽组件到这里
                      </div>
                    </template>
                  </Empty>
                </div>

                <!-- TabBar预览 -->
                <div v-if="editorState.hasTabBar && editorState.isPhonePreview" class="canvas-tabbar-preview">
                  <div class="tabbar-preview">
                    <div
                      v-for="item in tabBarItems"
                      :key="item.id"
                      class="tabbar-item"
                      :class="{ active: item.pageId === currentPageId }"
                      @click="switchTab(item.id)"
                    >
                      <div class="tabbar-icon">
                        <img v-if="item.icon" :src="item.icon" alt="" />
                        <div v-else class="default-icon">📱</div>
                      </div>
                      <div class="tabbar-title">{{ item.title }}</div>
                      <div v-if="item.badge" class="tabbar-badge">{{ item.badge }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </Spin>
          </div>
        </div>
      </LayoutContent>
    </Layout>

    <!-- 右侧属性面板 -->
    <LayoutSider theme="light" width="320" class="properties-sider">
      <div class="p-3">
        <div class="mb-3 text-lg font-semibold">属性配置</div>

        <div v-if="selectedComponent" class="properties-panel">
          <div class="mb-4 flex items-center justify-between">
            <div class="text-base font-medium">
              {{ selectedComponent.label || selectedComponent.type }}
              <Tag color="blue" class="ml-2">{{ selectedComponent.type }}</Tag>
            </div>
          </div>

          <Collapse default-active-key="props" ghost>
            <CollapsePanel key="props" header="组件属性">
              <Form :model="propsForm.model" layout="vertical">
                <FormItem
                  v-for="prop in selectedComponent.propsSchema"
                  :key="prop.key"
                  :label="prop.label"
                >
                  <component :is="renderPropEditor(prop)" />
                </FormItem>
                <FormItem
                  v-if="
                    !selectedComponent.propsSchema ||
                    selectedComponent.propsSchema.length === 0
                  "
                >
                  <div class="text-gray-500">该组件没有可配置属性</div>
                </FormItem>
              </Form>
            </CollapsePanel>

            <CollapsePanel key="style" header="样式设置">
              <div class="text-gray-500">样式设置功能开发中</div>
            </CollapsePanel>

            <CollapsePanel key="events" header="事件处理">
              <div class="text-gray-500">事件处理功能开发中</div>
            </CollapsePanel>
          </Collapse>
        </div>

        <Empty v-else description="请选择一个组件来编辑属性" />
      </div>
    </LayoutSider>

    <!-- 代码预览模态框 -->
    <Modal
      v-model:visible="editorState.codeModalVisible"
      title="组件代码"
      width="800px"
      :footer="null"
    >
      <pre class="code-preview">{{ generateComponentCode() }}</pre>
    </Modal>

    <!-- 页面预览抽屉 -->
    <Drawer
      v-model:visible="editorState.previewVisible"
      title="页面预览"
      width="390"
      placement="right"
    >
      <div class="preview-container">
        <div class="preview-header">
          <span
            >当前预览:
            {{ pageList.find((p) => p.id === currentPageId)?.title }}</span
          >
        </div>
        <div class="preview-tabs">
          <div class="preview-tabs-inner">
            <Tag
              v-for="page in pageList"
              :key="page.id"
              :color="currentPageId === page.id ? 'blue' : 'default'"
              class="preview-page-tag"
              @click="handleSwitchPage(page.id)"
            >
              {{ page.title }}
            </Tag>
          </div>
        </div>
        <div class="preview-phone">
          <div class="phone-header"></div>
          <div class="phone-content">
            <div
              v-for="comp in canvasComponents"
              :key="comp.id"
              class="preview-component"
            >
              <ComponentRenderer
                :type="comp.type"
                :component-props="comp.props"
                :style="comp.style"
              />
            </div>

            <Empty
              v-if="canvasComponents.length === 0"
              description="暂无内容"
            />
          </div>
          <div class="phone-footer"></div>
        </div>
      </div>
    </Drawer>

    <!-- 页面管理对话框 -->
    <Modal
      v-model:visible="editorState.pageManagerVisible"
      title="页面管理"
      width="600px"
    >
      <div class="page-manager">
        <div class="page-list">
          <div class="page-list-header">
            <div class="page-header-item page-id">页面ID</div>
            <div class="page-header-item page-title">页面标题</div>
            <div class="page-header-item page-path">页面路径</div>
            <div class="page-header-item page-action">操作</div>
          </div>
          <div v-for="page in pageList" :key="page.id" class="page-item">
            <div class="page-item-col page-id">{{ page.id }}</div>
            <div class="page-item-col page-title">{{ page.title }}</div>
            <div class="page-item-col page-path">{{ page.path }}</div>
            <div class="page-item-col page-action">
              <Button
                size="small"
                type="primary"
                class="mr-2"
                :disabled="currentPageId === page.id"
                @click="
                  handleSwitchPage(page.id);
                  editorState.pageManagerVisible = false;
                "
              >
                编辑
              </Button>
              <Button
                size="small"
                danger
                :disabled="currentPageId === page.id"
                @click="deletePage(page.id)"
              >
                删除
              </Button>
            </div>
          </div>
        </div>

        <Divider>添加新页面</Divider>

        <div class="add-page-form">
          <div class="form-row">
            <div class="form-label">页面ID:</div>
            <div class="form-field">
              <Input v-model:value="newPage.id" placeholder="如: about" />
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">页面标题:</div>
            <div class="form-field">
              <Input v-model:value="newPage.title" placeholder="如: 关于我们" />
            </div>
          </div>
          <div class="form-row">
            <div class="form-label">页面路径:</div>
            <div class="form-field">
              <Input
                v-model:value="newPage.path"
                placeholder="如: /pages/about"
              />
            </div>
          </div>
          <div class="form-row">
            <Button type="primary" @click="handleAddPage">添加页面</Button>
          </div>
        </div>
      </div>

      <template #footer>
        <Button @click="editorState.pageManagerVisible = false">关闭</Button>
      </template>
    </Modal>

    <!-- TabBar配置对话框 -->
    <TabBarConfig
      v-model:visible="editorState.tabBarConfigVisible"
      v-model:hasTabBar="editorState.hasTabBar"
      v-model:tabBarItems="tabBarItems"
      :page-list="pageList"
    />
  </Layout>
</template>

<style scoped>
.lowcode-editor {
  height: 100vh;
  background-color: #f0f2f5;
}

.materials-sider {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.materials-panel {
  height: 100%;
  overflow-y: auto;
  padding: 12px;
}

.component-list {
  max-height: calc(100vh - 240px);
  overflow-y: auto;
  padding-bottom: 20px;
}

.component-card {
  cursor: pointer;
  transition: all 0.3s;
}

.component-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.component-preview {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.component-placeholder {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e6f7ff;
  color: #1890ff;
  border-radius: 4px;
  font-size: 18px;
  font-weight: 500;
}

.header-toolbar {
  background-color: white;
  padding: 0 16px;
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

.toolbar-left {
  min-width: 180px;
  flex-shrink: 0;
}

.toolbar-right {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  flex: 1;
  gap: 8px;
  padding-bottom: 10px;
  margin-bottom: -10px;
  scrollbar-width: thin;
}

.toolbar-right::-webkit-scrollbar {
  height: 4px;
}

.toolbar-right::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.toolbar-right::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
}

.toolbar-right::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

.toolbar-item {
  display: flex;
  align-items: center;
  white-space: nowrap;
  flex-shrink: 0;
}

.toolbar-btn {
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.content-area {
  padding: 24px;
  overflow-y: auto;
}

.canvas-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  height: calc(100vh - 104px);
  display: flex;
  flex-direction: column;
}

.canvas-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.canvas-wrapper {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.canvas {
  width: 100%;
  min-height: 100%;
  padding: 16px;
  background-color: #fff;
}

.phone-preview .canvas-wrapper {
  background-color: #f5f5f5;
}

.phone-screen {
  width: 375px;
  height: calc(100% - 40px);
  margin: 0 auto;
  border: 10px solid #333;
  border-radius: 20px;
  position: relative;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
}

.canvas-with-tabbar .phone-screen {
  padding-bottom: 50px; /* 为TabBar预留空间 */
}

.canvas-tabbar-preview {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: white;
  border-top: 1px solid #eee;
  z-index: 10;
}

.tabbar-preview {
  display: flex;
  height: 100%;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  font-size: 12px;
  position: relative;
  transition: color 0.2s;
}

.tabbar-item:hover {
  color: #1989fa;
}

.tabbar-item.active {
  color: #1989fa;
}

.tabbar-icon {
  margin-bottom: 2px;
}

.tabbar-icon img {
  width: 20px;
  height: 20px;
}

.default-icon {
  font-size: 16px;
}

.tabbar-title {
  font-size: 10px;
}

.tabbar-badge {
  position: absolute;
  top: 2px;
  right: 8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
}

.canvas-draggable {
  min-height: 100%;
}

.component-wrapper {
  position: relative;
  margin-bottom: 10px;
  border: 1px dashed transparent;
  padding: 8px;
  transition: all 0.2s;
}

.component-wrapper.selected {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.05);
}

.component-controls {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(24, 144, 255, 0.1);
  z-index: 5;
  padding: 2px 8px;
}

.component-wrapper:hover .component-controls {
  display: block;
}

.drag-handle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  user-select: none;
}

.component-name {
  font-size: 12px;
  font-weight: 500;
  color: #1890ff;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-btn {
  font-size: 14px;
  color: #555;
  cursor: pointer;
  transition: color 0.2s;
}

.control-btn:hover {
  color: #1890ff;
}

.container-component {
  border: 1px dashed #ddd;
  padding: 16px;
  margin: 8px 0;
  min-height: 80px;
  position: relative;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.01);
}

.nested-draggable {
  min-height: 40px;
}

.nested-component {
  margin: 8px 0;
}

.layer-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.layer-list li {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.layer-list li:hover {
  background-color: #f5f5f5;
}

.layer-list li.active {
  background-color: #e6f7ff;
}

.layer-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.layer-list li:hover .layer-actions {
  opacity: 1;
}

.action-icon {
  cursor: pointer;
  margin-left: 8px;
  font-size: 16px;
  color: #999;
}

.action-icon:hover {
  color: #f5222d;
}

.delete-icon {
  color: #ff4d4f;
  font-weight: bold;
}

.properties-sider {
  border-left: 1px solid #f0f0f0;
  overflow-y: auto;
}

.properties-panel {
  margin-bottom: 24px;
}

.code-preview {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 500px;
  overflow-y: auto;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  padding: 8px 0;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.preview-tabs {
  padding-bottom: 12px;
  overflow-x: auto;
}

.preview-tabs-inner {
  display: flex;
  gap: 8px;
  padding-bottom: 4px;
}

.preview-page-tag {
  cursor: pointer;
}

.preview-phone {
  flex: 1;
  border: 10px solid #333;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: white;
  margin: 0 auto;
}

.phone-header {
  height: 24px;
  background-color: #333;
  position: relative;
}

.phone-header:after {
  content: '';
  position: absolute;
  width: 80px;
  height: 12px;
  background-color: #222;
  border-radius: 6px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.phone-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}

.phone-footer {
  height: 32px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  position: relative;
}

.preview-component {
  margin-bottom: 12px;
}

.page-manager {
  max-height: 500px;
  overflow-y: auto;
}

.page-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;
}

.page-list-header,
.page-item {
  display: grid;
  grid-template-columns: 1fr 1.5fr 2fr 1.5fr;
  gap: 8px;
  padding: 8px 12px;
  align-items: center;
}

.page-list-header {
  background-color: #f5f5f5;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
}

.page-item {
  border-bottom: 1px solid #f0f0f0;
}

.page-item:last-child {
  border-bottom: none;
}

.add-page-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-row {
  display: flex;
  align-items: center;
}

.form-label {
  width: 80px;
  flex-shrink: 0;
}

.form-field {
  flex: 1;
}

.empty-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}
</style>
