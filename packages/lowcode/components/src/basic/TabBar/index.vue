<script lang="ts" setup>
import type { PropType } from 'vue';

import { computed, ref, watch } from 'vue';

import { Tabbar, TabbarItem } from 'vant';
import { IconifyIcon } from '@vben-core/icons';

export interface TabItem {
  /**
   * 徽标内容
   */
  badge?: number | string;
  /**
   * 是否显示徽标小红点
   */
  dot?: boolean;
  /**
   * 图标名称（支持iconify格式，如：mdi:home）
   */
  icon?: string;
  /**
   * 标签名称，作为匹配的标识符
   */
  name: string;
  /**
   * 绑定的页面ID
   */
  pageId?: string;
  /**
   * 对应的页面路径（兼容旧版本）
   */
  path?: string;
  /**
   * 标题
   */
  title: string;
}

const props = defineProps({
  /**
   * 切换标签前的回调函数，返回 false 可阻止切换，支持返回 Promise
   */
  beforeChange: {
    default: null,
    type: Function as PropType<(name: string) => boolean | Promise<boolean>>,
  },
  /**
   * 是否显示外边框
   */
  border: {
    default: true,
    type: Boolean,
  },
  /**
   * 是否处于设计器环境
   */
  designMode: {
    default: false,
    type: Boolean,
  },
  /**
   * 是否固定在底部
   */
  fixed: {
    default: true,
    type: Boolean,
  },
  /**
   * 当前选中的标签的名称
   */
  modelValue: {
    default: '',
    type: String,
  },
  /**
   * 固定在底部时，是否在标签位置生成一个等高的占位元素
   */
  placeholder: {
    default: false,
    type: Boolean,
  },
  /**
   * 是否开启底部安全区适配
   */
  safeAreaInsetBottom: {
    default: true,
    type: Boolean,
  },
  /**
   * 标签栏数据
   */
  tabs: {
    default: () => [],
    type: Array as PropType<TabItem[]>,
  },
  /**
   * TabBar变体类型
   */
  variant: {
    default: 'bottom',
    type: String as PropType<'bottom' | 'top' | 'side'>,
  },
  /**
   * 元素的z-index
   */
  zIndex: {
    default: 1,
    type: Number,
  },
});

const emit = defineEmits(['update:modelValue', 'change', 'navigate', 'pageSwitch']);

const activeTab = ref(props.modelValue);

// 检查是否是iconify图标格式
function isIconifyIcon(iconName: string) {
  return iconName && iconName.includes(':');
}

// 监听外部传入的 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    activeTab.value = newVal;
  },
);

// 检测是否在低代码设计器中运行
const isInDesigner = computed(() => {
  return (
    props.designMode ||
    window.location.href.includes('designer') ||
    window.location.href.includes('editor')
  );
});

// 处理标签变化
const handleChange = async (name: string) => {
  // 如果提供了 beforeChange 回调，先执行它
  if (props.beforeChange) {
    const canChange = await props.beforeChange(name);
    if (canChange === false) return;
  }

  activeTab.value = name;
  emit('update:modelValue', name);
  emit('change', name);

  // 找到对应的标签
  const selectedTab = props.tabs.find((tab) => tab.name === name);
  if (selectedTab) {
    // 优先使用pageId进行页面切换
    if (selectedTab.pageId) {
      emit('pageSwitch', selectedTab.pageId, selectedTab);
    }
    // 兼容旧版本的path导航
    else if (selectedTab.path) {
      emit('navigate', selectedTab.path, selectedTab);
    }
  }
};
</script>

<template>
  <div
    class="tab-bar-wrapper"
    :class="{
      'in-designer': isInDesigner,
      [`variant-${variant}`]: true
    }"
  >
    <Tabbar
      :value="activeTab"
      :border="border"
      :fixed="isInDesigner ? false : fixed"
      :placeholder="placeholder"
      :safe-area-inset-bottom="safeAreaInsetBottom"
      :z-index="zIndex"
      @change="handleChange"
    >
      <TabbarItem
        v-for="tab in tabs"
        :key="tab.name"
        :name="tab.name"
        :dot="tab.dot"
        :badge="tab.badge"
      >
        <template #icon>
          <IconifyIcon
            v-if="isIconifyIcon(tab.icon)"
            :icon="tab.icon"
            class="iconify-icon"
          />
          <img v-else-if="tab.icon && tab.icon.startsWith('http')" :src="tab.icon" alt="" />
          <span v-else-if="tab.icon" class="van-icon" :class="`van-icon-${tab.icon}`"></span>
        </template>
        {{ tab.title }}
      </TabbarItem>
    </Tabbar>

    <!-- 在设计器中显示绑定的页面提示 -->
    <div v-if="isInDesigner && activeTab" class="tab-page-hint">
      <div
        v-if="tabs.find((t) => t.name === activeTab)?.pageId"
        class="bound-page"
      >
        已绑定页面ID: {{ tabs.find((t) => t.name === activeTab)?.pageId }}
      </div>
      <div
        v-else-if="tabs.find((t) => t.name === activeTab)?.path"
        class="bound-page"
      >
        已绑定页面路径: {{ tabs.find((t) => t.name === activeTab)?.path }}
      </div>
      <div v-else class="no-bound-page">未绑定页面</div>
    </div>
  </div>
</template>

<style scoped>
.tab-bar-wrapper {
  width: 100%;
}

/* 在设计器中的特殊样式 */
.in-designer {
  position: relative;
  height: 70px; /* 加高以显示页面提示 */
}

.in-designer :deep(.van-tabbar) {
  position: absolute !important;
  bottom: 20px;
  left: 0;
  right: 0;
}

.tab-page-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  font-size: 12px;
  text-align: center;
  color: #999;
  background-color: #f5f5f5;
  border-top: 1px dashed #ddd;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 8px;
}

.bound-page {
  color: #2196f3;
}

.no-bound-page {
  color: #ff9800;
}

/* iconify图标样式 */
.iconify-icon {
  width: 22px;
  height: 22px;
  font-size: 22px;
}

/* 不同变体的样式 */
.variant-top .van-tabbar {
  top: 0;
  bottom: auto;
}

.variant-side .van-tabbar {
  flex-direction: column;
  width: 80px;
  height: 100vh;
  left: 0;
  right: auto;
  bottom: 0;
}

.variant-side .van-tabbar-item {
  flex-direction: column;
  padding: 8px 4px;
}
</style>
