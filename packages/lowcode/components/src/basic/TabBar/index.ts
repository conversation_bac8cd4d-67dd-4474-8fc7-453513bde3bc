import TabBarVue from './index.vue';

export const TabBarComponent = {
  category: 'navigation',
  defaultProps: {
    border: true,
    designMode: false,
    fixed: true,
    modelValue: 'home',
    placeholder: false,
    safeAreaInsetBottom: true,
    tabs: [
      { icon: 'mdi:home', name: 'home', pageId: 'home', title: '首页' },
      { icon: 'mdi:magnify', name: 'search', pageId: 'search', title: '搜索' },
      { icon: 'mdi:account', name: 'user', pageId: 'user', title: '我的' },
    ],
    zIndex: 1,
    variant: 'bottom', // 新增：TabBar变体类型
    visiblePages: [], // 新增：指定在哪些页面显示，空数组表示在所有页面显示
  },
  icon: 'mdi:tab',
  label: '底部标签栏',
  propsSchema: [
    {
      key: 'variant',
      label: 'TabBar类型',
      options: [
        { label: '底部标签栏', value: 'bottom' },
        { label: '顶部标签栏', value: 'top' },
        { label: '侧边标签栏', value: 'side' },
      ],
      type: 'select',
    },
    {
      itemSchema: [
        { key: 'name', label: '标识', type: 'input' },
        { key: 'title', label: '标题', type: 'input' },
        { key: 'icon', label: '图标', type: 'input', description: 'iconify图标名称，如：mdi:home' },
        {
          key: 'pageId',
          label: '绑定页面',
          optionsProvider: 'getRegisteredPages',
          type: 'select',
        },
        { key: 'badge', label: '徽标', type: 'input' },
        { key: 'dot', label: '红点', type: 'switch' },
      ],
      key: 'tabs',
      label: '标签项',
      type: 'array',
    },
    {
      key: 'modelValue',
      label: '当前选中标签',
      options: [
        { label: '首页', value: 'home' },
        { label: '搜索', value: 'search' },
        { label: '我的', value: 'user' },
      ],
      type: 'select',
    },
    {
      key: 'border',
      label: '显示边框',
      type: 'switch',
    },
    {
      key: 'fixed',
      label: '固定在底部',
      type: 'switch',
    },
    {
      key: 'placeholder',
      label: '占位元素',
      type: 'switch',
    },
    {
      key: 'safeAreaInsetBottom',
      label: '安全区适配',
      type: 'switch',
    },
    {
      key: 'zIndex',
      label: 'z-index',
      type: 'number',
    },
    {
      key: 'visiblePages',
      label: '显示页面',
      type: 'select',
      mode: 'multiple',
      optionsProvider: 'getRegisteredPages',
      description: '选择TabBar在哪些页面显示，不选择则在所有页面显示',
    },
  ],
  renderer: {
    component: TabBarVue,
    options: {
      designMode: true,
    },
  },
  type: 'TabBar',
};

export default TabBarComponent;
