import type { ComponentDefinition } from '../../../../core/src/types';

import TabBarVue from './index.vue';

export const SideTabBarComponent: ComponentDefinition = {
  category: 'navigation',
  defaultProps: {
    border: true,
    designMode: false,
    fixed: true,
    modelValue: 'home',
    placeholder: false,
    safeAreaInsetBottom: false,
    tabs: [
      { icon: 'mdi:home', name: 'home', pageId: '', title: '首页' },
      { icon: 'mdi:magnify', name: 'search', pageId: '', title: '搜索' },
      { icon: 'mdi:account', name: 'user', pageId: '', title: '我的' },
    ],
    zIndex: 1000,
    variant: 'side',
  },
  icon: 'mdi:tab-unselected',
  label: '侧边标签栏',
  propsSchema: [
    {
      key: 'variant',
      label: 'TabBar类型',
      options: [
        { label: '底部标签栏', value: 'bottom' },
        { label: '顶部标签栏', value: 'top' },
        { label: '侧边标签栏', value: 'side' },
      ],
      type: 'select',
    },
    {
      itemSchema: [
        { key: 'name', label: '标识', type: 'input' },
        { key: 'title', label: '标题', type: 'input' },
        { key: 'icon', label: '图标', type: 'input', description: 'iconify图标名称，如：mdi:home' },
        {
          key: 'pageId',
          label: '绑定页面',
          optionsProvider: 'getRegisteredPages',
          type: 'select',
        },
        { key: 'badge', label: '徽标', type: 'input' },
        { key: 'dot', label: '红点', type: 'switch' },
      ],
      key: 'tabs',
      label: '标签项',
      type: 'array',
    },
    {
      key: 'modelValue',
      label: '当前选中标签',
      options: [
        { label: '首页', value: 'home' },
        { label: '搜索', value: 'search' },
        { label: '我的', value: 'user' },
      ],
      type: 'select',
    },
    {
      key: 'border',
      label: '显示边框',
      type: 'switch',
    },
    {
      key: 'fixed',
      label: '固定在侧边',
      type: 'switch',
    },
    {
      key: 'placeholder',
      label: '占位元素',
      type: 'switch',
    },
    {
      key: 'zIndex',
      label: 'z-index',
      type: 'number',
    },
  ],
  renderer: {
    component: TabBarVue,
    options: {
      designMode: true,
    },
  },
  type: 'SideTabBar',
};

export default SideTabBarComponent;
