<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { PageRenderer } from '@vben/lowcode/core';
import type { ProjectConfig } from '@vben/lowcode/core';

// 模拟从API获取的项目配置数据
const projectSchema = ref<ProjectConfig | null>(null);
const currentPath = ref('/pages/home');
const loading = ref(true);

// 模拟API调用获取项目配置
async function fetchProjectConfig() {
  try {
    loading.value = true;
    
    // 这里应该是实际的API调用
    // const response = await fetch('/api/project/config');
    // const data = await response.json();
    
    // 模拟数据
    const mockData: ProjectConfig = {
      id: 'demo-project',
      name: '演示项目',
      version: '1.0.0',
      pages: [
        {
          id: 'home',
          title: '首页',
          path: '/pages/home',
          components: [
            {
              id: 'text-1',
              type: 'text',
              props: {
                content: '欢迎使用低代码平台',
                fontSize: '18px',
                color: '#333',
              },
              style: {
                textAlign: 'center',
                padding: '20px',
              },
            },
            {
              id: 'button-1',
              type: 'button',
              props: {
                text: '点击我',
                type: 'primary',
                size: 'large',
              },
              style: {
                margin: '20px auto',
                display: 'block',
              },
            },
          ],
        },
        {
          id: 'category',
          title: '分类',
          path: '/pages/category',
          components: [
            {
              id: 'text-2',
              type: 'text',
              props: {
                content: '这是分类页面',
                fontSize: '16px',
              },
              style: {
                padding: '20px',
              },
            },
          ],
        },
        {
          id: 'cart',
          title: '购物车',
          path: '/pages/cart',
          components: [
            {
              id: 'text-3',
              type: 'text',
              props: {
                content: '这是购物车页面',
                fontSize: '16px',
              },
              style: {
                padding: '20px',
              },
            },
          ],
        },
        {
          id: 'user',
          title: '我的',
          path: '/pages/user',
          components: [
            {
              id: 'text-4',
              type: 'text',
              props: {
                content: '这是个人中心页面',
                fontSize: '16px',
              },
              style: {
                padding: '20px',
              },
            },
          ],
        },
      ],
      tabbarConfig: {
        tabs: [
          {
            id: 'tab-home',
            title: '首页',
            icon: '/icons/home.png',
            pageId: 'home',
            route: '/pages/home',
          },
          {
            id: 'tab-category',
            title: '分类',
            icon: '/icons/category.png',
            pageId: 'category',
            route: '/pages/category',
          },
          {
            id: 'tab-cart',
            title: '购物车',
            icon: '/icons/cart.png',
            pageId: 'cart',
            route: '/pages/cart',
            badge: 2,
          },
          {
            id: 'tab-user',
            title: '我的',
            icon: '/icons/user.png',
            pageId: 'user',
            route: '/pages/user',
          },
        ],
        activeTabId: 'tab-home',
        fixed: true,
        height: 50,
      },
      globalConfig: {
        theme: {
          primaryColor: '#1989fa',
        },
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    projectSchema.value = mockData;
    
  } catch (error) {
    console.error('Failed to fetch project config:', error);
  } finally {
    loading.value = false;
  }
}

// 处理导航
function handleNavigate(path: string) {
  currentPath.value = path;
  console.log('Navigate to:', path);
}

// 处理Tab切换
function handleTabSwitch(tabId: string, tab: any) {
  console.log('Tab switched:', tabId, tab);
}

onMounted(() => {
  fetchProjectConfig();
});
</script>

<template>
  <div class="h5-app">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <p>加载中...</p>
    </div>
    
    <!-- 页面渲染器 -->
    <PageRenderer
      v-else-if="projectSchema"
      :project-schema="projectSchema"
      :current-path="currentPath"
      :preview="true"
      @navigate="handleNavigate"
      @tab-switch="handleTabSwitch"
    />
    
    <!-- 错误状态 -->
    <div v-else class="error">
      <p>加载失败，请刷新重试</p>
    </div>
  </div>
</template>

<style scoped>
.h5-app {
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.loading,
.error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #666;
  font-size: 16px;
}

.error {
  color: #f56565;
}
</style>
