import type { PageSchema, TabbarConfig, ComponentSchema } from '../renderer/PageRenderer.vue';

/**
 * 项目配置接口
 */
export interface ProjectConfig {
  id: string;
  name: string;
  version: string;
  pages: PageSchema[];
  globalConfig?: {
    theme?: Record<string, any>;
    api?: Record<string, any>;
    [key: string]: any;
  };
  tabbarConfig?: TabbarConfig;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 项目管理器
 * 负责项目数据的序列化、反序列化和转换
 */
export class ProjectManager {
  private project: ProjectConfig;

  constructor(project?: ProjectConfig) {
    this.project = project || this.createEmptyProject();
  }

  /**
   * 创建空项目
   */
  private createEmptyProject(): ProjectConfig {
    return {
      id: `project-${Date.now()}`,
      name: '未命名项目',
      version: '1.0.0',
      pages: [
        {
          id: 'home',
          title: '首页',
          path: '/pages/home',
          components: [],
          layout: 'default',
        },
      ],
      globalConfig: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * 获取项目配置
   */
  getProject(): ProjectConfig {
    return { ...this.project };
  }

  /**
   * 更新项目配置
   */
  updateProject(updates: Partial<ProjectConfig>): void {
    this.project = {
      ...this.project,
      ...updates,
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * 添加页面
   */
  addPage(page: PageSchema): void {
    this.project.pages.push(page);
    this.project.updatedAt = new Date().toISOString();
  }

  /**
   * 更新页面
   */
  updatePage(pageId: string, updates: Partial<PageSchema>): boolean {
    const pageIndex = this.project.pages.findIndex(p => p.id === pageId);
    if (pageIndex === -1) return false;

    this.project.pages[pageIndex] = {
      ...this.project.pages[pageIndex],
      ...updates,
    };
    this.project.updatedAt = new Date().toISOString();
    return true;
  }

  /**
   * 删除页面
   */
  removePage(pageId: string): boolean {
    const initialLength = this.project.pages.length;
    this.project.pages = this.project.pages.filter(p => p.id !== pageId);
    
    if (this.project.pages.length !== initialLength) {
      this.project.updatedAt = new Date().toISOString();
      return true;
    }
    return false;
  }

  /**
   * 更新页面组件
   */
  updatePageComponents(pageId: string, components: ComponentSchema[]): boolean {
    return this.updatePage(pageId, { components });
  }

  /**
   * 设置TabBar配置
   */
  setTabbarConfig(config: TabbarConfig): void {
    this.project.tabbarConfig = config;
    this.project.updatedAt = new Date().toISOString();
  }

  /**
   * 从设计器数据转换为项目配置
   * 这个方法用于将设计器的数据格式转换为标准的项目配置格式
   */
  static fromDesignerData(designerData: {
    pageList: Array<{ id: string; title: string; path: string }>;
    pagesComponentsMap: Map<string, any[]>;
    tabbarConfig?: any;
  }): ProjectConfig {
    const pages: PageSchema[] = designerData.pageList.map(page => ({
      id: page.id,
      title: page.title,
      path: page.path,
      components: designerData.pagesComponentsMap.get(page.id) || [],
      layout: 'default',
    }));

    return {
      id: `project-${Date.now()}`,
      name: '低代码项目',
      version: '1.0.0',
      pages,
      tabbarConfig: designerData.tabbarConfig,
      globalConfig: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * 转换为设计器数据格式
   * 这个方法用于将项目配置转换为设计器可以使用的数据格式
   */
  toDesignerData(): {
    pageList: Array<{ id: string; title: string; path: string }>;
    pagesComponentsMap: Map<string, any[]>;
    tabbarConfig?: TabbarConfig;
  } {
    const pageList = this.project.pages.map(page => ({
      id: page.id,
      title: page.title,
      path: page.path,
    }));

    const pagesComponentsMap = new Map<string, any[]>();
    this.project.pages.forEach(page => {
      pagesComponentsMap.set(page.id, page.components);
    });

    return {
      pageList,
      pagesComponentsMap,
      tabbarConfig: this.project.tabbarConfig,
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON(): string {
    return JSON.stringify(this.project, null, 2);
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: string): ProjectManager {
    try {
      const project = JSON.parse(json) as ProjectConfig;
      return new ProjectManager(project);
    } catch (error) {
      console.error('Failed to parse project JSON:', error);
      return new ProjectManager();
    }
  }

  /**
   * 验证项目配置
   */
  validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.project.id) {
      errors.push('项目ID不能为空');
    }

    if (!this.project.name) {
      errors.push('项目名称不能为空');
    }

    if (!this.project.pages || this.project.pages.length === 0) {
      errors.push('项目至少需要一个页面');
    }

    // 验证页面
    this.project.pages.forEach((page, index) => {
      if (!page.id) {
        errors.push(`页面 ${index + 1} 缺少ID`);
      }
      if (!page.title) {
        errors.push(`页面 ${index + 1} 缺少标题`);
      }
      if (!page.path) {
        errors.push(`页面 ${index + 1} 缺少路径`);
      }
    });

    // 验证TabBar配置
    if (this.project.tabbarConfig) {
      const { tabs } = this.project.tabbarConfig;
      if (!tabs || tabs.length === 0) {
        errors.push('TabBar配置中至少需要一个Tab');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * 创建项目管理器实例
 */
export function createProjectManager(project?: ProjectConfig): ProjectManager {
  return new ProjectManager(project);
}
