import type { Component as VueComponent } from 'vue';

import type { Message } from './communication';

/**
 * 页面信息接口
 */
export interface PageInfo {
  id: string;
  path: string;
  title: string;
}

export interface ComponentLifecycle<P = any> {
  // 组件销毁前
  onBeforeDestroy?: (props: P) => void;

  // 组件创建时
  onCreate?: (props: P) => void;

  // 组件挂载时
  onMount?: (props: P, element: HTMLElement) => void;

  // 组件更新时
  onUpdate?: (oldProps: P, newProps: P) => void;

  // 组件进入可视区域时
  onVisibilityChange?: (props: P, isVisible: boolean) => void;
}
/**
 * 组件定义接口
 */
export interface ComponentDefinition<P = any> {
  category: string; // 组件分类
  // 添加通信配置
  communication?: {
    listenTo?: string[]; // 监听的消息类型
    onMessage?: (message: Message, props: P) => void;
  };
  // 数据绑定配置
  dataBindings?: {
    // 支持绑定的属性
    bindableProps: string[];

    // 数据源配置
    dataSources?: Array<{
      // 其他配置
      config?: Record<string, any>;
      type: 'api' | 'expression' | 'variable';
    }>;

    // 事件处理
    eventHandlers?: Record<
      string,
      Array<{
        // 动作配置
        config: Record<string, any>;
        type: 'callApi' | 'navigate' | 'setData';
      }>
    >;
  };
  // 默认属性
  defaultProps: P;
  // 设计时特有配置（不影响实际渲染）
  designerConfig?: {
    // 设计时的辅助信息
    guidelines?: any;

    // 设计时的特殊处理
    handlers?: any;

    // 设计时的交互限制
    restrictions?: any;
  };

  icon: string; // 组件图标

  // 是否可以包含子组件
  isContainer?: boolean;

  label: string; // 组件显示名称

  // 添加生命周期钩子
  lifecycle?: ComponentLifecycle<P>;

  // 属性编辑器配置
  propsSchema: PropSchema[];

  // 组件渲染信息
  renderer: {
    // 组件名称（Vue组件或自定义组件）
    component: VueComponent;

    // 可选的渲染配置
    options?: Record<string, any>;
  };

  // 样式编辑器配置
  styleSchema?: StyleSchema[];

  // 组件基本信息
  type: string; // 组件类型标识
}

/**
 * 属性编辑器配置
 */
export interface PropSchema {
  description?: string;
  key: string;
  label: string;
  options?: Array<{ label: string; value: any }>;
  type:
    | 'array'
    | 'color'
    | 'complex'
    | 'input'
    | 'number'
    | 'select'
    | 'switch'
    | 'textarea';
}

/**
 * 样式编辑器配置
 */
export interface StyleSchema {
  key: string;
  label: string;
  options?: Array<{ label: string; value: any }>;
  type: 'color' | 'input' | 'number' | 'select';
}

/**
 * 组件实例接口
 */
export interface Component {
  children?: Component[];
  id: string;
  props?: Record<string, any>;
  style?: Record<string, any>;
  type: string;
}

/**
 * 组件类型
 */
export type ComponentType =
  | 'button'
  | 'container'
  | 'image'
  | 'input'
  | 'table'
  | 'text'
  | 'TabBar'
  | 'TopTabBar'
  | 'SideTabBar';

/**
 * 属性配置项
 */
export interface PropConfig {
  description?: string;
  key: string;
  label: string;
  options?: Array<{ label: string; value: any }>;
  type:
    | 'array'
    | 'color'
    | 'complex'
    | 'input'
    | 'number'
    | 'select'
    | 'switch'
    | 'textarea';
}

/**
 * 组件类别
 */
export enum ComponentCategory {
  BASIC = 'basic',
  BUSINESS = 'business',
  DATA = 'data',
  FORM = 'form',
  LAYOUT = 'layout',
  NAVIGATION = 'navigation',
}

/**
 * 组件类别配置
 */
export interface CategoryConfig {
  key: string;
  label: string;
}

/**
 * 组件类别列表
 */
export const COMPONENT_CATEGORIES: CategoryConfig[] = [
  { key: ComponentCategory.BASIC, label: '基础组件' },
  { key: ComponentCategory.FORM, label: '表单组件' },
  { key: ComponentCategory.LAYOUT, label: '布局组件' },
  { key: ComponentCategory.NAVIGATION, label: '导航组件' },
  { key: ComponentCategory.DATA, label: '数据展示' },
  { key: ComponentCategory.BUSINESS, label: '业务组件' },
];

/**
 * 渲染上下文
 */
export interface RenderContext {
  // 全局数据
  data?: Record<string, any>;

  // 事件处理器
  eventHandlers?: Record<string, Function>;

  // 运行时状态
  runtime?: {
    isDesignTime?: boolean; // 是否为设计时渲染
    isPreview?: boolean; // 是否为预览模式
    scale?: number; // 缩放比例
  };
}
