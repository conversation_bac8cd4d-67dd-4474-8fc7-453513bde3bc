/**
 * @vben/lowcode-core
 * 低代码平台核心组件库
 */

// 导出适配器
export * from './adapters';

// 导出API模块
export * from './api';

// 导出组件注册中心
// export * from './components';

// 导出上下文系统
export * from './context';

// 导出领域模块
export * from './domains';

// 导出钩子
export * from './hooks';

// 导出注册中心
export * from './registry';

// 导出渲染引擎
export * from './renderer';

// 导出组件渲染器
export { default as ComponentRenderer } from './renderer/ComponentRenderer.vue';

// 导出页面渲染器
export { default as PageRenderer } from './renderer/PageRenderer.vue';

// 导出设置函数
export * from './setup';

// 导出初始化函数
export * from './setup/initialize';

// 导出状态管理
export * from './store';

// 导出项目管理
export * from './project';

// 导出类型定义
export * from './types';

// 导出工具函数
export * from './utils';

/**
 * 库版本号
 */
export const VERSION = '1.0.0';
