<script setup lang="ts">
import type { PropType } from 'vue';

import { computed, provide, ref } from 'vue';

import ComponentRenderer from './ComponentRenderer.vue';

export interface PageSchema {
  id: string;
  title: string;
  path: string;
  components: ComponentSchema[];
  layout?: 'default' | 'tabbar';
  tabbarConfig?: TabbarConfig;
}

export interface ComponentSchema {
  id: string;
  type: string;
  props: Record<string, any>;
  style?: Record<string, any>;
  children?: ComponentSchema[];
}

export interface TabbarConfig {
  tabs: TabItem[];
  activeTabId: string;
  fixed: boolean;
  height: number;
}

export interface TabItem {
  id: string;
  title: string;
  icon?: string;
  route?: string;
  badge?: number | string;
  dot?: boolean;
  pageId?: string; // 关联的页面ID
}

const props = defineProps({
  /**
   * 项目配置 - 包含所有页面和全局配置
   */
  projectSchema: {
    type: Object as PropType<{
      pages: PageSchema[];
      globalConfig?: Record<string, any>;
      tabbarConfig?: TabbarConfig;
    }>,
    required: true,
  },
  /**
   * 当前页面路径
   */
  currentPath: {
    type: String,
    default: '/',
  },
  /**
   * 是否为预览模式
   */
  preview: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['navigate', 'tabSwitch']);

// 当前激活的页面
const currentPage = computed(() => {
  return props.projectSchema.pages.find(
    page => page.path === props.currentPath
  ) || props.projectSchema.pages[0];
});

// 是否有TabBar布局
const hasTabbar = computed(() => {
  return props.projectSchema.tabbarConfig || 
         currentPage.value?.layout === 'tabbar';
});

// TabBar配置
const tabbarConfig = computed(() => {
  return props.projectSchema.tabbarConfig || currentPage.value?.tabbarConfig;
});

// 当前激活的Tab ID
const activeTabId = ref(tabbarConfig.value?.activeTabId || '');

// 根据TabBar获取当前应该显示的页面
const displayPage = computed(() => {
  if (!hasTabbar.value || !tabbarConfig.value) {
    return currentPage.value;
  }

  // 如果有TabBar，根据当前激活的Tab找到对应的页面
  const activeTab = tabbarConfig.value.tabs.find(tab => tab.id === activeTabId.value);
  if (activeTab?.pageId) {
    const tabPage = props.projectSchema.pages.find(page => page.id === activeTab.pageId);
    if (tabPage) return tabPage;
  }

  return currentPage.value;
});

// 处理Tab切换
function handleTabSwitch(tabId: string) {
  activeTabId.value = tabId;
  const tab = tabbarConfig.value?.tabs.find(t => t.id === tabId);
  
  if (tab?.route) {
    emit('navigate', tab.route);
  }
  
  if (tab?.pageId) {
    const targetPage = props.projectSchema.pages.find(p => p.id === tab.pageId);
    if (targetPage) {
      emit('navigate', targetPage.path);
    }
  }
  
  emit('tabSwitch', tabId, tab);
}

// 获取iconify图标组件
function getIconComponent(iconName: string) {
  if (!iconName.includes(':')) return null;

  const [prefix, name] = iconName.split(':');
  const componentName = prefix.charAt(0).toUpperCase() + prefix.slice(1) +
    name.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('');

  return componentName;
}

// 提供全局上下文
provide('pageContext', {
  currentPage: displayPage,
  projectSchema: props.projectSchema,
  preview: props.preview,
});
</script>

<template>
  <div class="page-renderer" :class="{ 'page-renderer--preview': preview }">
    <!-- 页面内容区域 -->
    <div 
      class="page-renderer__content"
      :class="{
        'page-renderer__content--with-tabbar': hasTabbar,
      }"
    >
      <!-- 渲染当前页面的组件 -->
      <template v-if="displayPage?.components">
        <ComponentRenderer
          v-for="component in displayPage.components"
          :key="component.id"
          :type="component.type"
          :component-props="component.props"
          :style="component.style"
          :id="component.id"
          :children="component.children"
        />
      </template>
      
      <!-- 空页面提示 -->
      <div v-else class="page-renderer__empty">
        <p>{{ preview ? '页面暂无内容' : '拖拽组件到此处' }}</p>
      </div>
    </div>

    <!-- TabBar -->
    <div 
      v-if="hasTabbar && tabbarConfig" 
      class="page-renderer__tabbar"
      :class="{
        'page-renderer__tabbar--fixed': tabbarConfig.fixed,
      }"
      :style="{ height: `${tabbarConfig.height || 50}px` }"
    >
      <div
        v-for="tab in tabbarConfig.tabs"
        :key="tab.id"
        class="page-renderer__tab"
        :class="{
          'page-renderer__tab--active': tab.id === activeTabId,
        }"
        @click="handleTabSwitch(tab.id)"
      >
        <div v-if="tab.icon" class="page-renderer__tab-icon">
          <component
            v-if="tab.icon.includes(':')"
            :is="getIconComponent(tab.icon)"
            class="iconify-icon"
          />
          <img v-else :src="tab.icon" :alt="tab.title" />
        </div>
        <div class="page-renderer__tab-title">{{ tab.title }}</div>
        <div v-if="tab.badge" class="page-renderer__tab-badge">{{ tab.badge }}</div>
        <div v-else-if="tab.dot" class="page-renderer__tab-dot"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-renderer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  position: relative;
}

.page-renderer__content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.page-renderer__content--with-tabbar {
  padding-bottom: 66px; /* TabBar高度 + 间距 */
}

.page-renderer__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}

.page-renderer__tabbar {
  display: flex;
  background-color: #fff;
  border-top: 1px solid #ebedf0;
  z-index: 1000;
}

.page-renderer__tabbar--fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

.page-renderer__tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  color: #646566;
  font-size: 12px;
  padding: 8px 4px;
  transition: color 0.2s;
}

.page-renderer__tab--active {
  color: #1989fa;
}

.page-renderer__tab-icon {
  margin-bottom: 4px;
  font-size: 22px;
  height: 22px;
}

.page-renderer__tab-icon img {
  width: 22px;
  height: 22px;
  display: block;
}

.page-renderer__tab-icon .iconify-icon {
  width: 22px;
  height: 22px;
  font-size: 22px;
  display: block;
}

.page-renderer__tab-title {
  margin-top: 2px;
}

.page-renderer__tab-badge {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  min-width: 16px;
  padding: 0 4px;
  color: #fff;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  background-color: #ee0a24;
  border-radius: 8px;
}

.page-renderer__tab-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: #ee0a24;
  border-radius: 100%;
  transform: translate(50%, -50%);
}

/* 预览模式样式 */
.page-renderer--preview {
  max-width: 375px;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}
</style>
