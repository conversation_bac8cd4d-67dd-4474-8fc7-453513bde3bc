import type { VNode } from 'vue';

import type { Component, RenderContext } from '../types';

import { h } from 'vue';

import { componentRegistry } from '../components';

/**
 * 渲染组件
 * @param component 组件实例
 * @param context 渲染上下文
 * @returns Vue虚拟节点
 */
export function renderComponent(
  component: Component,
  context: RenderContext = {},
): null | VNode {
  const definition = componentRegistry.getComponentDefinition(component.type);
  if (!definition) {
    console.error(`未找到组件定义: ${component.type}`);
    return null;
  }

  // 从组件定义中获取Vue组件
  const { component: Component } = definition.renderer;
  if (!Component) {
    console.error(`组件 ${component.type} 没有关联的渲染器`);
    return null;
  }

  // 合并默认属性和实际属性
  const props = {
    ...definition.defaultProps,
    ...component.props,
  };

  // 数据绑定处理
  const resolvedProps = resolveDataBindings(props, context.data || {});

  // 创建子节点
  const children: VNode[] = [];
  if (component.children && component.children.length > 0) {
    for (const child of component.children) {
      const childNode = renderComponent(child, context);
      if (childNode) {
        children.push(childNode);
      }
    }
  }

  // 处理样式
  const style = component.style || {};

  // 创建Vue虚拟节点
  return h(
    Component,
    {
      ...resolvedProps,
      key: component.id,
      style,
    },
    () => (children.length > 0 ? children : undefined),
  );
}

/**
 * 解析数据绑定
 * 支持 {{expression}} 语法，将表达式替换为实际数据
 * @param props 组件属性对象
 * @param data 数据源
 * @returns 解析后的属性对象
 */
export function resolveDataBindings(
  props: Record<string, any>,
  data: Record<string, any>,
): Record<string, any> {
  const result = { ...props };

  // 遍历所有属性，查找并解析绑定表达式
  for (const key in result) {
    const value = result[key];
    if (
      typeof value === 'string' &&
      value.includes('{{') &&
      value.includes('}}')
    ) {
      result[key] = resolveExpression(value, data);
    }
  }

  return result;
}

/**
 * 解析表达式
 * @param expression 表达式字符串，如 "Hello, {{name}}"
 * @param data 数据对象
 * @returns 解析后的值
 */
function resolveExpression(expression: string, data: Record<string, any>): any {
  // 简单模式：替换 {{key}} 为 data[key]
  return expression.replaceAll(/\{\{(.*?)\}\}/g, (_, key) => {
    const trimmedKey = key.trim();
    return data[trimmedKey] === undefined ? '' : data[trimmedKey];
  });
}

/**
 * 解析事件处理器
 * @param component 组件实例
 * @param context 渲染上下文
 * @returns 事件处理器对象
 */
export function resolveEventHandlers(
  component: Component,
  context: RenderContext,
): Record<string, (...args: any[]) => void> {
  const result: Record<string, (...args: any[]) => void> = {};
  const definition = componentRegistry.getComponentDefinition(component.type);

  if (!definition || !definition.dataBindings?.eventHandlers) {
    return result;
  }

  // 获取组件定义中支持的事件列表
  const supportedEvents = Object.keys(definition.dataBindings.eventHandlers);

  // 为每个事件创建处理函数
  for (const eventName of supportedEvents) {
    const handlerName =
      component.props?.[
        `on${eventName.charAt(0).toUpperCase() + eventName.slice(1)}`
      ];
    if (handlerName && context.eventHandlers?.[handlerName]) {
      result[eventName] = (...args: any[]) => {
        context.eventHandlers?.[handlerName]?.(...args);
      };
    }
  }

  return result;
}

// 导出组件渲染器
export { default as ComponentRenderer } from './ComponentRenderer.vue';

// 导出页面渲染器
export { default as PageRenderer } from './PageRenderer.vue';
