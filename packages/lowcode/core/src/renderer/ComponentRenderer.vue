<script setup lang="ts">
import {
  computed,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  ref,
  watch,
} from 'vue';

import { useCommunication } from '../communication';
import { componentRegistry } from '../registry';

// 定义组件属性
const props = defineProps<{
  // 子组件
  children?: any[];
  // 组件属性
  componentProps?: Record<string, any>;
  // 组件ID
  id: string;
  // 组件样式
  style?: Record<string, any>;
  // 组件类型
  type: string;
}>();

// 定义事件
const emit = defineEmits(['pageSwitch', 'navigate']);

const componentRef = ref<HTMLElement | null>(null);

// 获取组件定义
const componentDef = computed(() => {
  return componentRegistry.getComponentDefinition(props.type);
});

const bus = useCommunication();
// 合并属性
const mergedProps = computed(() => {
  const defaultProps = componentDef.value?.defaultProps || {};
  return {
    ...defaultProps,
    ...props.componentProps,
    // 注入组件ID
    componentId: props.id,
    // 注入发送消息的方法
    onSendMessage: (type: string, payload: any, target?: string) => {
      bus.send({
        payload,
        source: props.id,
        target,
        type,
      });
    },
    style: {
      ...componentDef.value?.defaultProps?.style,
      ...props.style,
    },
    // 传递子组件
    children: props.children,
    // 事件处理器
    onPageSwitch: (pageId: string, tabInfo: any) => {
      emit('pageSwitch', pageId, tabInfo);
    },
    onNavigate: (path: string, info: any) => {
      emit('navigate', path, info);
    },
  };
});

// 是否找到组件
const hasComponent = computed(() => !!componentDef.value);
// 是否是容器组件
const isContainer = computed(() => componentDef.value?.isContainer === true);

// 监听消息
onMounted(() => {
  if (componentDef.value?.communication?.listenTo) {
    const unsubscribe = bus.listen(props.id, (message) => {
      // 调用组件定义中的消息处理函数
      if (componentDef.value?.communication?.onMessage) {
        componentDef.value.communication.onMessage(message, mergedProps.value);
      }
    });

    // 组件卸载时取消监听
    onUnmounted(() => {
      unsubscribe();
    });
  }
});

// 处理生命周期
onMounted(() => {
  if (componentDef.value?.lifecycle?.onMount && componentRef.value) {
    componentDef.value.lifecycle.onMount(mergedProps.value, componentRef.value);
  }

  // 设置可见性观察器
  if (componentDef.value?.lifecycle?.onVisibilityChange) {
    const observer = new IntersectionObserver((entries) => {
      const isVisible = entries[0]?.isIntersecting ?? false;
      componentDef.value?.lifecycle?.onVisibilityChange?.(
        mergedProps.value,
        isVisible,
      );
    });

    if (componentRef.value) {
      observer.observe(componentRef.value);
    }

    onBeforeUnmount(() => {
      observer.disconnect();
    });
  }
});

// 监听属性变化
const prevProps = ref(mergedProps.value);
watch(mergedProps, (newProps) => {
  if (componentDef.value?.lifecycle?.onUpdate) {
    componentDef.value.lifecycle.onUpdate(prevProps.value, newProps);
  }
  prevProps.value = { ...newProps };
});

onBeforeUnmount(() => {
  if (componentDef.value?.lifecycle?.onBeforeDestroy) {
    componentDef.value.lifecycle.onBeforeDestroy(mergedProps.value);
  }
});
</script>

<template>
  <component
    v-if="hasComponent"
    :is="componentDef?.renderer?.component"
    v-bind="mergedProps"
    ref="componentRef"
  >
    <!-- 如果是容器组件且有子组件，渲染子组件 -->
    <template v-if="isContainer && children && children.length > 0">
      <ComponentRenderer
        v-for="child in children"
        :key="child.id"
        :id="child.id"
        :type="child.type"
        :component-props="child.props"
        :style="child.style"
        :children="child.children"
        @pageSwitch="(pageId, tabInfo) => emit('pageSwitch', pageId, tabInfo)"
        @navigate="(path, info) => emit('navigate', path, info)"
      />
    </template>
  </component>
  <div v-else class="component-not-found">未找到组件: {{ type }}</div>
</template>

<style scoped>
.component-not-found {
  padding: 10px;
  border: 1px dashed #ff4d4f;
  color: #ff4d4f;
  background-color: #fff1f0;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
}
</style>
