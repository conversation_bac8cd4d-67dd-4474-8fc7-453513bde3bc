import type {
  ComponentDefinition,
  ComponentType,
  PageInfo,
  PropConfig,
} from '../types';

import { markRaw } from 'vue';

/**
 * 组件注册中心
 * 负责管理所有可用的低代码组件
 */
class ComponentRegistry {
  // 组件定义映射
  private components: Map<string, ComponentDefinition> = new Map();

  // 页面列表
  private pages: PageInfo[] = [];

  // 清空所有组件
  clear(): void {
    this.components.clear();
  }

  // 获取所有分类
  getAllCategories(): string[] {
    const categories = new Set<string>();
    for (const def of this.getAllComponentDefinitions()) {
      categories.add(def.category);
    }
    return [...categories];
  }

  // 获取所有组件定义
  getAllComponentDefinitions(): ComponentDefinition[] {
    return [...this.components.values()];
  }

  // 获取所有页面
  getAllPages(): PageInfo[] {
    return [...this.pages];
  }

  // 获取组件定义
  getComponentDefinition(type: string): ComponentDefinition | undefined {
    return this.components.get(type);
  }

  // 获取组件属性配置
  getComponentPropsConfig(type: string): PropConfig[] {
    const definition = this.getComponentDefinition(type);
    if (!definition) return [];
    return definition.propsSchema.map((schema) => ({
      description: schema.description,
      key: schema.key,
      label: schema.label,
      options: schema.options,
      type: schema.type,
    }));
  }

  // 按分类获取组件定义
  getComponentsByCategory(category: string): ComponentDefinition[] {
    return this.getAllComponentDefinitions().filter(
      (def) => def.category === category,
    );
  }

  // 根据ID获取页面
  getPageById(id: string): PageInfo | undefined {
    return this.pages.find((page) => page.id === id);
  }

  // 根据路径获取页面
  getPageByPath(path: string): PageInfo | undefined {
    return this.pages.find((page) => page.path === path);
  }

  // 注册组件
  register(definition: ComponentDefinition): void {
    // 确保组件是标记为raw的，避免Vue的响应式包装
    const rawComponent = markRaw(definition.renderer.component);

    // 创建组件定义的副本，并将组件标记为raw
    const def = {
      ...definition,
      renderer: {
        ...definition.renderer,
        component: rawComponent,
      },
    };

    this.components.set(definition.type, def);
  }

  // 批量注册组件
  registerBatch(definitions: ComponentDefinition[]): void {
    definitions.forEach((def) => this.register(def));
  }

  // 注册页面
  registerPage(page: PageInfo): void {
    // 检查是否已存在相同ID的页面
    const existingIndex = this.pages.findIndex((p) => p.id === page.id);
    if (existingIndex === -1) {
      // 添加新页面
      this.pages.push(page);
    } else {
      // 更新已存在的页面
      this.pages[existingIndex] = page;
    }
  }

  // 批量注册页面
  registerPages(pages: PageInfo[]): void {
    pages.forEach((page) => this.registerPage(page));
  }

  // 移除页面
  removePage(id: string): boolean {
    const initialLength = this.pages.length;
    this.pages = this.pages.filter((page) => page.id !== id);
    return initialLength !== this.pages.length;
  }

  // 移除组件
  unregister(type: string): boolean {
    return this.components.delete(type);
  }
}

// 导出单例实例
export const componentRegistry = new ComponentRegistry();

/**
 * 获取组件属性配置
 * @param type 组件类型
 * @returns 属性配置数组
 */
export function getComponentPropsConfig(type: ComponentType): PropConfig[] {
  return componentRegistry.getComponentPropsConfig(type);
}

/**
 * 获取所有已注册页面
 * @returns 页面信息数组
 */
export function getAllPages(): PageInfo[] {
  return componentRegistry.getAllPages();
}

/**
 * 获取所有已注册页面作为选项列表
 * 用于属性编辑器中的动态选项
 * @returns 选项列表
 */
export function getRegisteredPages(): Array<{ label: string; value: string }> {
  const pages = componentRegistry.getAllPages();
  return pages.map((page) => ({
    label: `${page.title} (${page.path})`,
    value: page.id, // 使用页面ID而不是路径
  }));
}
